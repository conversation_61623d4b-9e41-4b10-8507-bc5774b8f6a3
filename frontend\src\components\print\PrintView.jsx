import React, { forwardRef } from "react";
import logo from "../models/abee_white.png";

/**
 * PrintView - Reusable Print Component
 *
 * A unified print component that can handle different types of print layouts:
 * - Bill printing (POS receipts)
 * - Invoice printing (formal invoices)
 * - Voucher printing (payment/receive vouchers)
 *
 * Usage Examples:
 *
 * 1. Bill Printing:
 * <PrintView
 *   ref={printRef}
 *   printType="bill"
 *   products={products}
 *   totals={totals}
 *   customerInfo={customer}
 *   companyDetails={company}
 *   receivedAmount={paid}
 *   balanceAmount={balance}
 *   paymentType="cash"
 *   billNumber="BILL-001"
 *   formatCurrency={(amount) => `Rs. ${amount}`}
 * />
 *
 * 2. Invoice Printing:
 * <PrintView
 *   ref={printRef}
 *   printType="invoice"
 *   invoiceData={invoiceData}
 *   formatCurrency={(amount) => `Rs. ${amount}`}
 * />
 *
 * 3. Custom Template:
 * <PrintView
 *   ref={printRef}
 *   template={customTemplate}
 *   renderDynamicTemplate={renderFunction}
 *   products={products}
 *   totals={totals}
 * />
 */

const PrintView = forwardRef(({
  // Bill/Invoice data
  billData = {},
  products = [],
  totals = {},
  customerInfo = {},
  companyDetails = {},
  
  // Payment information
  receivedAmount = 0,
  balanceAmount = 0,
  paymentType = "cash",
  
  // Bill details
  billNumber = "",
  billTime = null,
  
  // Template and styling
  template = null,
  renderDynamicTemplate = null,
  
  // Utility functions
  formatCurrency = (amount) => `Rs. ${parseFloat(amount || 0).toFixed(2)}`,
  
  // Print type (bill, invoice, voucher)
  printType = "bill", // bill, invoice, voucher
  
  // Additional props for different print types
  voucherData = {},
  invoiceData = {},

  // Custom styling options
  showLogo = true,
  showTerms = true,
  showFooter = true,
  customStyles = {},
}, ref) => {
  
  // Get current user for cashier info
  const getCurrentUser = () => {
    try {
      const storedUser = localStorage.getItem("user") || sessionStorage.getItem("user");
      if (storedUser) {
        const user = JSON.parse(storedUser);
        return user.name || user.username || "Admin";
      }
      return "Admin";
    } catch {
      return "Admin";
    }
  };

  // Render Bill Print View
  const renderBillPrint = () => (
    <>
      {/* Bill Header */}
      {showLogo && (
        <div className="text-center bill-header">
          <img
            src={logo}
            alt="Logo"
            className="p-0 mx-auto my-0"
            style={{
              width: "250px",
              height: "auto",
              objectFit: "contain",
              ...customStyles.logo,
            }}
          />
          <div>{companyDetails.business_address}</div>
          <div>{companyDetails.contact_number}</div>
          <div>{companyDetails.email}</div>
          <hr className="my-1 border-t border-black" />
        </div>
      )}

      {/* Bill Information */}
      <div className="grid grid-cols-2 gap-2 mt-2 text-sm bill-info">
        <div>
          <strong>Customer:</strong>{" "}
          <b>{customerInfo?.name || "Cash Customer"}</b>
        </div>
        <div>
          <strong>Bill No:</strong> <b>{billNumber}</b>
        </div>
        <div>
          <strong>Cashier:</strong> <b>{getCurrentUser()}</b>
        </div>
        <div>
          <strong>Date:</strong> <b>{new Date().toLocaleDateString()}</b>
        </div>
        <div>
          <strong>Payment:</strong> <b>{paymentType}</b>
        </div>
        <div>
          <strong>Time:</strong>
          <b>{" "}
          {billTime ||
            new Date().toLocaleTimeString("en-IN", {
              hour: "2-digit",
              minute: "2-digit",
              second: "2-digit",
              hour12: true,
            })}</b>
        </div>
      </div>

      {/* Products Table */}
      <table className="w-full mt-2 text-sm border-collapse bill-table">
        <thead>
          <tr className="bg-gray-100">
            <th className="px-2 py-1 text-left">No</th>
            <th className="px-2 py-1 text-left">Name</th>
            <th className="px-2 py-1 text-right">Price</th>
            <th className="px-2 py-1 text-right">Dis</th>
            <th className="px-2 py-1 text-right">Total</th>
          </tr>
        </thead>
        <tbody>
          {products.map((product, index) => (
            <React.Fragment key={index}>
              {/* No and Product Name Row - Same Line */}
              <tr>
                <td className="px-2 py-1 text-left" style={{ fontWeight: "bold", borderBottom: "none" }}>
                  {index + 1}
                </td>
                <td
                  colSpan={4}
                  className="px-2 py-1"
                  style={{
                    fontWeight: "bold",
                    wordWrap: "break-word",
                    overflowWrap: "break-word",
                    lineHeight: "1.2",
                    borderBottom: "none",
                    textAlign: "left"
                  }}
                >
                  {product.product_name}
                </td>
              </tr>
              {/* Quantity and Product Details Row */}
              <tr>
                <td className="px-2 py-1" style={{ borderTop: "none" }}></td>
                <td className="px-2 py-1 text-left" style={{ fontWeight: "bold", borderTop: "none" }}>
                  {product.qty || 1}
                </td>
                <td className="px-2 py-1 text-right" style={{ fontWeight: "bold", borderTop: "none" }}>
                  {(
                    (product.mrp)
                  ).toFixed(2)}
                </td>
                <td className="px-2 py-1 text-right" style={{ fontWeight: "bold", borderTop: "none" }}>
                  {(
                    (product.discount || 0) + (product.specialDiscount || 0)
                  ).toFixed(2)}
                </td>
                <td className="px-2 py-1 font-semibold text-right" style={{ fontWeight: "bold", borderTop: "none" }}>
                  {(
                    (product.mrp - (product.discount || 0) / (product.qty || 1)) *
                      product.qty - (product.specialDiscount || 0)
                  ).toFixed(2)}
                </td>
              </tr>
            </React.Fragment>
          ))}
        </tbody>
      </table>

      <hr className="my-1 border-t border-black" />

      {/* Billing Summary */}
      <div
        style={{
          padding: "8px",
          borderRadius: "4px",
          backgroundColor: "#F9FAFB",
        }}
      >
        <h3
          style={{
            paddingBottom: "5px",
            fontSize: "18px",
            fontWeight: "600",
            color: "#1F2937",
          }}
        >
          Billing Summary
        </h3>
        <div
          style={{
            paddingTop: "4px",
            marginTop: "5px",
            borderTop: "1px solid #E5E7EB",
          }}
        >
          <p style={{ display: "flex", justifyContent: "space-between", marginBottom: "4px" }}>
            <strong style={{ textAlign: "left", color: "#1F2937" }}>Subtotal:</strong>
            <strong style={{ fontSize: "16px" }}>
              {formatCurrency(totals.subTotalMRP?.toFixed(2))}
            </strong>
          </p>
          <p style={{ display: "flex", justifyContent: "space-between", marginBottom: "4px" }}>
            <strong style={{ textAlign: "left", color: "#1F2937" }}>Total Discounts:</strong>
            <span style={{ fontSize: "14px" }}>
              {formatCurrency(totals.totalItemDiscounts)}
            </span>
          </p>
          {totals.totalBillDiscount > 0 && (
            <p style={{ display: "flex", justifyContent: "space-between", marginBottom: "4px" }}>
              <strong style={{ textAlign: "left", color: "#1F2937" }}>Bill Discount:</strong>
              <span style={{ fontSize: "14px" }}>
                {formatCurrency(totals.totalBillDiscount)}
              </span>
            </p>
          )}
          <p style={{ display: "flex", justifyContent: "space-between", marginBottom: "4px" }}>
            <strong style={{ textAlign: "left", color: "#1F2937" }}>Grand Total:</strong>
            <strong style={{ fontSize: "18px" }}>
              {formatCurrency(totals.finalTotal?.toFixed(2))}
            </strong>
          </p>
          <p style={{ display: "flex", justifyContent: "space-between", marginBottom: "4px" }}>
            <strong style={{ textAlign: "left", color: "#1F2937" }}>Paid:</strong>
            <span style={{ fontSize: "14px" }}>
              {formatCurrency(receivedAmount.toFixed(2))}
            </span>
          </p>
          <p style={{ display: "flex", justifyContent: "space-between", marginBottom: "0" }}>
            <strong style={{ textAlign: "left", color: "#1F2937" }}>Balance:</strong>
            <strong style={{ fontSize: "16px" }}>
              {formatCurrency(balanceAmount.toFixed(2))}
            </strong>
          </p>
        </div>
      </div>

      {/* Terms and Conditions */}
      {showTerms && (
        <div className="mt-2 text-xs text-left terms-conditions">
          <h4 className="font-bold text-center">Terms and Conditions</h4>
          <p className="text-center">
            Exchange is allowed within 7 days with original bill.
          </p>
        </div>
      )}

      {/* Footer */}
      {showFooter && (
        <>
          <p className="mt-2 font-semibold text-center thanks">
            Thank You! Visit Again.
          </p>
          <p className="font-bold text-center text-[14px]">System by IMSS</p>
          <p className="text-[10px] font-bold text-center systemby-webs">visit🔗: www.imss.lk | 0752233855</p>
        </>
      )}
    </>
  );

  // Render Invoice Print View (placeholder for future implementation)
  const renderInvoicePrint = () => (
    <div className="text-center">
      <p>Invoice printing functionality will be implemented here</p>
    </div>
  );

  return (
    <div ref={ref} className="print-container">
      {template && renderDynamicTemplate ? (
        renderDynamicTemplate(template)
      ) : (
        <>
          {printType === "bill" && renderBillPrint()}
          {printType === "invoice" && renderInvoicePrint()}
        </>
      )}

      {/* Print Styles */}
      <style jsx global>{`
        @media print {
          body {
            margin: 0;
            padding: 0;
            background: white;
            color: #000;
          }

          .print-container {
            width: 100%;
            margin: 0;
            padding: 0;
          }

          /* Header styles */
          .bill-header {
            margin-bottom: 3px;
            text-align: center;
          }
          .bill-header img {
            max-width: 60mm;
            height: auto;
            margin: 0 auto 3px auto;
          }
          .shop-name {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 2px;
            font-family: "Cressida", Elephant, cursive;
          }

          /* Table styles */
          .bill-table {
            width: 100%;
            border-collapse: collapse;
            margin: 3px 0;
          }
          .bill-table th,
          .bill-table td {
            padding: 2px 4px;
            font-size: 12px;
          }
          .bill-table th {
            background-color: #f0f0f0;
            font-weight: bold;
          }

          /* Info styles */
          .bill-info {
            margin: 3px 0;
            font-size: 12px;
          }

          /* Terms and footer */
          .terms-conditions {
            margin-top: 5px;
            font-size: 10px;
          }
          .thanks {
            margin: 3px 0;
            font-size: 12px;
            text-align: center;
          }
          .systemby,
          .systemby-web {
            font-size: 8px;
            text-align: center;
            margin: 1px 0;
          }

          /* Invoice specific styles */
          .printable-invoice {
            width: 100%;
            margin: 0;
            padding: 10mm;
          }

          /* Hide non-print elements */
          .no-print {
            display: none !important;
          }

          /* Color adjustments for print */
          .bg-blue-600 { background-color: #2563eb !important; }
          .text-blue-600 { color: #2563eb !important; }
          .border-blue-600 { border-color: #2563eb !important; }
          .bg-gray-100 { background-color: #f3f4f6 !important; }
          .text-gray-700 { color: #374151 !important; }
          .border-gray-200 { border-color: #e5e7eb !important; }
        }
      `}</style>
    </div>
  );
});

PrintView.displayName = "PrintView";

export default PrintView;
