import React, {
  useState,
  useRef,
  useEffect,
  useCallback,
  useMemo,
} from "react";
import html2pdf from "html2pdf.js";
import Select from "react-select";
import axios from "axios";
import { toast } from "react-toastify";
import PrintableQuotation from "./PrintableQuotation";
import { useAuth } from "../../context/NewAuthContext";
import { FiChevronDown, FiChevronUp } from "react-icons/fi";

// Define custom styles for react-select
const customSelectStyles = {
  control: (provided, state) => ({
    ...provided,
    borderColor: state.isFocused ? "#2563eb" : "#d1d5db",
    boxShadow: state.isFocused ? "0 0 0 1px #2563eb" : "none",
    minHeight: "42px",
    borderRadius: "0.375rem",
    backgroundColor: state.isDisabled ? "#f3f4f6" : "white",
    "&:hover": {
      borderColor: "#2563eb",
    },
    fontSize: "0.875rem",
    color: "#111827",
    ...(state.selectProps.isDarkMode && {
      borderColor: state.isFocused ? "#60a5fa" : "#4b5563",
      backgroundColor: state.isDisabled ? "#374151" : "#1f2937",
      color: "#f9fafb",
    }),
  }),
  menu: (provided, state) => ({
    ...provided,
    zIndex: 9999,
    fontSize: "0.875rem",
    backgroundColor: "white",
    border: "1px solid #d1d5db",
    borderRadius: "0.375rem",
    ...(state.selectProps.isDarkMode && {
      backgroundColor: "#1f2937",
      border: "1px solid #4b5563",
    }),
  }),
  option: (provided, state) => ({
    ...provided,
    backgroundColor: state.isSelected
      ? "#2563eb"
      : state.isFocused
        ? "#e0e7ff"
        : "white",
    color: state.isSelected ? "white" : "#111827",
    cursor: "pointer",
    "&:hover": {
      backgroundColor: state.isSelected ? "#2563eb" : "#e0e7ff",
    },
    ...(state.selectProps.isDarkMode && {
      backgroundColor: state.isSelected
        ? "#3b82f6"
        : state.isFocused
          ? "#4b5563"
          : "#1f2937",
      color: state.isSelected ? "white" : "#f9fafb",
    }),
  }),
  placeholder: (provided, state) => ({
    ...provided,
    color: "#6b7280",
    ...(state.selectProps.isDarkMode && {
      color: "#9ca3af",
    }),
  }),
  singleValue: (provided, state) => ({
    ...provided,
    color: "#111827",
    ...(state.selectProps.isDarkMode && {
      color: "#f9fafb",
    }),
  }),
};

// Utility function to format quotation number as QT-XXX
const formatQuotationNumber = (no) => {
  if (!no) return "QT-000"; // Fallback for empty or null
  // If the number already includes 'QT-', return it as is
  if (no.startsWith("QT-")) return no;
  const num = parseInt(no, 10);
  if (isNaN(num)) return `QT-${no}`; // Fallback for non-numeric values
  return `QT-${String(num).padStart(3, "0")}`; // Format as QT-001, QT-002, etc.
};

const Quotation = () => {
  const { user } = useAuth();
  
  // Helper function to get authentication headers
  const getAuthHeaders = () => {
    let token = user?.token;
    if (!token) {
      let userData = null;
      if (localStorage.getItem("user")) {
        userData = JSON.parse(localStorage.getItem("user"));
      } else if (sessionStorage.getItem("user")) {
        userData = JSON.parse(sessionStorage.getItem("user"));
      }
      token = userData?.token;
    }
    return token ? { Authorization: `Bearer ${token}` } : {};
  };

  // Utility function to get current time in Sri Lanka timezone in "HH:mm" format
  const getSriLankaTime = () => {
    const options = {
      hour: "2-digit",
      minute: "2-digit",
      hour12: false,
      timeZone: "Asia/Colombo",
    };
    return new Intl.DateTimeFormat("en-GB", options).format(new Date());
  };

  const [quotations, setQuotations] = useState([]);

  const [showForm, setShowForm] = useState(false);
  const [showPrintPreview, setShowPrintPreview] = useState(false);
  const [showPrintPreviewMinimized, setShowPrintPreviewMinimized] =
    useState(false);
  const [editingQuotationId, setEditingQuotationId] = useState(null);
  const [customer, setCustomer] = useState({
    id: null,
    name: "",
    address: "",
    phone: "",
    email: "",
  });
  const [agent, setAgent] = useState({
    name: "",
    phone: "",
  });
  const [items, setItems] = useState([]);
  const [newItem, setNewItem] = useState({
    productId: null,
    variantId: null,
    description: "",
    qty: 1,
    mrp: 0,
    sellingPrice: 0,
    freeQty: 0,
    specialDiscountAmount: 0,
    discountAmount: 0,
  });
  const [tax, setTax] = useState(0); // Added tax state
  const [isDiscountManuallyEdited, setIsDiscountManuallyEdited] =
    useState(false);
  const [products, setProducts] = useState([]);
  const [customers, setCustomers] = useState([]);
  const [customersLoading, setCustomersLoading] = useState(false);
  const [discountSchemes, setDiscountSchemes] = useState([]);
  const [errors, setErrors] = useState({});
  const [searchTerm, setSearchTerm] = useState("");
  const [productSearchTerm, setProductSearchTerm] = useState("");
  const [expandedRows, setExpandedRows] = useState([]);
  // const [expandedRows, setExpandedRows] = useState([]);
  const [users, setUsers] = useState([]);
  const [isLoadingUsers, setIsLoadingUsers] = useState(false);
  const [errorUsers, setErrorUsers] = useState(null);
  const [footerDetails, setFooterDetails] = useState({
    preparedBy: user?.name || "",
    approvedBy: "",
  });
  const [quotation, setQuotation] = useState({
    no: "",
    date: new Date().toISOString().split("T")[0],
    time: getSriLankaTime(), // "HH:mm" 24-hour format Sri Lanka time
  });

  const printableRef = useRef();
  const customerNameRef = useRef(null);
  const customerAddressRef = useRef(null);
  const customerPhoneRef = useRef(null);
  const customerEmailRef = useRef(null);
  const newItemProductSelectRef = useRef(null);
  const newItemQtyRef = useRef(null);
  const newItemMrpRef = useRef(null);
  const newItemSellingPriceRef = useRef(null);
  const newItemFreeQtyRef = useRef(null);
  useEffect(() => {
    console.log("User object from useAuth:", user);
  }, [user]);

  useEffect(() => {
    const updateTime = () => {
      setQuotation((prev) => ({
        ...prev,
        time: getSriLankaTime(),
      }));
    };
    updateTime();
  }, []);
  const handleProductSelectKeyDown = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      // If a product is selected, move focus to quantity input
      if (newItem.productId) {
        e.preventDefault();
        setTimeout(() => {
          newItemQtyRef.current?.focus();
          newItemQtyRef.current?.select();
        }, 0);
      }
    }
    // Allow arrow keys to work natively in react-select
  };
  useEffect(() => {
    const fetchUsers = async () => {
      setIsLoadingUsers(true);
      setErrorUsers(null);
      try {
        if (!user?.token) {
          throw new Error("No authentication token found.");
        }
        const response = await axios.get(
          "http://127.0.0.1:8000/api/quotation-users",
          { headers: getAuthHeaders() }
        );
        setUsers(response.data);
      } catch (error) {
        console.error("Error fetching users:", error);
        setErrorUsers(error.message || "Failed to load users for dropdown.");
        toast.error(error.message || "Failed to load users for dropdown.");
      } finally {
        setIsLoadingUsers(false);
      }
    };

    if (user?.token) {
      fetchUsers();
    } else {
      setErrorUsers("User not authenticated.");
      toast.error("Please log in to load users.");
    }
  }, [user?.token]);
  // Fetch initial data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setCustomersLoading(true);
        const [
          productsResponse,
          schemesResponse,
          customersResponse,
          quotationsResponse,
        ] = await Promise.all([
          axios.get("http://127.0.0.1:8000/api/products", { headers: getAuthHeaders() }),
          axios.get("http://127.0.0.1:8000/api/discount-schemes", { headers: getAuthHeaders() }),
          axios.get("http://127.0.0.1:8000/api/customers", { headers: getAuthHeaders() }),
          axios.get("http://127.0.0.1:8000/api/quotations", { headers: getAuthHeaders() }),
        ]);

        const productsData =
          productsResponse.data.data || productsResponse.data;
        setProducts(
          productsData.map((product) => ({
            product_id: product.product_id,
            product_name: product.product_name,
            description: product.description || "",
            mrp: parseFloat(product.mrp) || 0,
            sales_price: parseFloat(product.sales_price) || 0,
            opening_stock_quantity: parseFloat(
              product.opening_stock_quantity || 0
            ),
            category: product.category || "Unknown",
            category_name: product.category || "Unknown",
            supplier: product.supplier || "",
            store_location: product.store_location || "",
            // Include variants for batch selection
            variants: product.variants || [],
          }))
        );

        const schemesData = schemesResponse.data.data || schemesResponse.data;
        setDiscountSchemes(schemesData);

        const customersData =
          customersResponse.data.data || customersResponse.data;
        setCustomers(customersData);

        const quotationsData =
          quotationsResponse.data.data || quotationsResponse.data;
        setQuotations(
          quotationsData.map((q) => ({
            id: q.id,
            customer: {
              id: q.customer.id,
              name: q.customer.customer_name,
              address: q.customer.address || "",
              phone: q.customer.phone || "",
              email: q.customer.email || "",
            },
            agent: {
              name: q.agent_name || "",
              phone: q.agent_phone || "",
            },
            items: q.items.map((item) => ({
              productId: item.product_id,
              description: item.description,
              qty: item.qty,
              mrp: parseFloat(item.mrp),
              sellingPrice: parseFloat(item.selling_price),
              freeQty: item.free_qty,
              discountAmount: parseFloat(item.discount_amount),
              specialDiscountAmount: parseFloat(item.special_discount_amount),
              discountPercentage: parseFloat(item.discount_percentage),
              discountSchemeType: item.discount_scheme_type,
              total: parseFloat(item.total),
            })),
            total: parseFloat(q.total),
            date: q.date,
            quotation: {
              no: q.quotation_no,
              date: q.date,
              time: q.time,
            },
            footerDetails: {
              receivedBy: q.received_by || "",
              preparedBy: q.prepared_by || "",
              checkedBy: q.checked_by || "",
              authorizedBy: q.authorized_by || "",
              approved: q.approved || "",
              date: q.date || "",
              time: q.time || "",
            },
          }))
        );
      } catch (error) {
        console.error("Error fetching data:", error);
        toast.error(
          "Failed to load data: " +
            (error.response?.data?.error || error.message)
        );
      } finally {
        setCustomersLoading(false);
      }
    };
    fetchData();
  }, []);

  const handleQuotationChange = (e) => {
    const { name, value } = e.target;
    setQuotation({ ...quotation, [name]: value });
  };

  const handleCustomerChange = (e) => {
    const { name, value } = e.target;
    setCustomer({ ...customer, [name]: value });
  };
  const handleApprovedByChange = (selectedOption) => {
    setFooterDetails((prev) => ({
      ...prev,
      approvedBy: selectedOption ? selectedOption.value : "",
    }));
  };

  const handleCustomerSelect = (selectedOption) => {
    const customerData = selectedOption
      ? customers.find((c) => c.id === selectedOption.value)
      : null;
    setCustomer({
      id: customerData ? customerData.id : null,
      name: customerData ? customerData.customer_name : "",
      address: customerData ? customerData.address || "" : "",
      phone: customerData ? customerData.phone || "" : "",
      email: customerData ? customerData.email || "" : "",
    });
    setErrors((prev) => ({
      ...prev,
      customerName: undefined,
      customerEmail: undefined,
    }));
  };

  const handleAgentChange = (e) => {
    const { name, value } = e.target;
    setAgent({ ...agent, [name]: value });
  };

  const isDateWithinScheme = (quotationDate, startDate, endDate) => {
    try {
      const quoteDate = new Date(quotationDate);
      const start = startDate ? new Date(startDate) : null;
      const end = endDate ? new Date(endDate) : null;
      return (!start || quoteDate >= start) && (!end || quoteDate <= end);
    } catch (e) {
      console.error("Error checking date within scheme:", e);
      return false;
    }
  };

  const calculateSpecialDiscount = useCallback(
    (item, quotationDate) => {
      if (!item.productId) {
        return { discount: 0, scheme: null, schemeType: null };
      }

      const baseProduct = products.find((p) => p.product_id === item.productId);
      if (!baseProduct) {
        console.warn(`Product not found for productId: ${item.productId}`);
        return { discount: 0, scheme: null, schemeType: null };
      }

      // If item has variantId, find the specific variant, otherwise use the base product
      let product;
      if (
        item.variantId &&
        baseProduct.variants &&
        baseProduct.variants.length > 0
      ) {
        const variant = baseProduct.variants.find(
          (v) => v.product_variant_id === item.variantId
        );
        if (variant) {
          // Merge base product with variant data
          product = {
            ...baseProduct,
            ...variant,
            variant_id: variant.product_variant_id,
            batch_number: variant.batch_number,
            expiry_date: variant.expiry_date,
            sales_price: variant.sales_price,
            mrp: variant.mrp,
          };
        } else {
          console.warn(`Variant not found for variantId: ${item.variantId}`);
          product = baseProduct;
        }
      } else {
        product = baseProduct;
      }

      const categoryName =
        product.category_name || product.category || "Unknown";
      if (categoryName === "Unknown") {
        console.warn(`No valid category for product: ${product.product_name}`);
      }

      const qty = parseFloat(item.qty) || 1;
      const mrp = parseFloat(item.mrp) || 0;
      const totalAmount = qty * mrp;

      const applicableScheme = discountSchemes.find((scheme) => {
        if (
          !scheme.active ||
          !isDateWithinScheme(quotationDate, scheme.start_date, scheme.end_date)
        ) {
          return false;
        }

        const target = scheme.target?.trim().toLowerCase();

        // Create display name for batch-wise matching
        // Use item data first (from cart), then fall back to product data (from items array)
        const batchNumber = item.batch_number || product.batch_number;
        const expiryDate = item.expiry_date || product.expiry_date;

        const batchInfo = batchNumber ? ` (Batch: ${batchNumber})` : "";
        const expiryInfo = expiryDate
          ? ` (Exp: ${expiryDate.split("T")[0]})`
          : "";
        const displayName = `${product.product_name}${batchInfo}${expiryInfo}`;

        const productMatch =
          scheme.applies_to === "product" &&
          (target === (product.description?.trim().toLowerCase() || "") ||
            target === (product.product_name?.trim().toLowerCase() || "") ||
            target === displayName.trim().toLowerCase());

        const categoryMatch =
          scheme.applies_to === "category" &&
          categoryName &&
          target === categoryName?.trim().toLowerCase();

        return productMatch || categoryMatch;
      });

      if (!applicableScheme) {
        return { discount: 0, scheme: null, schemeType: null };
      }

      let discount = 0;
      if (applicableScheme.type === "percentage") {
        discount = (totalAmount * parseFloat(applicableScheme.value)) / 100;
      } else if (applicableScheme.type === "amount") {
        discount = parseFloat(applicableScheme.value) * qty;
      }

      const schemeType =
        applicableScheme.applies_to === "product" ? "product" : "category";

      return {
        discount: discount >= 0 ? discount : 0,
        scheme: applicableScheme,
        schemeType,
      };
    },
    [products, discountSchemes]
  );

  // Update item totals including special discount (similar to SalesInvoice.jsx)
  const updateItemTotal = useCallback(
    (item, quotationDate) => {
      const qty = parseFloat(item.qty) || 0;
      const mrp = parseFloat(item.mrp) || 0;
      const sellingPrice = parseFloat(item.sellingPrice) || 0;

      // Calculate special discount
      const {
        discount: specialDiscount,
        scheme,
        schemeType,
      } = calculateSpecialDiscount(item, quotationDate);

      // Calculate base discount (normal discount)
      const baseDiscountPerUnit = mrp - sellingPrice;
      const baseDiscount = baseDiscountPerUnit * qty;
      const baseDiscountNonNegative = baseDiscount >= 0 ? baseDiscount : 0;

      // Total discount = base discount + special discount
      let totalDiscountAmount = baseDiscountNonNegative + specialDiscount;

      // Use manual discountAmount if it was manually edited
      if (
        typeof item.discountAmount === "number" &&
        item.discountAmount !== 0 &&
        item.isDiscountManuallyEdited
      ) {
        totalDiscountAmount = item.discountAmount;
      }

      // Calculate total
      const total = qty * mrp - totalDiscountAmount;

      // Calculate discount percentage
      const discountPercentage =
        qty * mrp > 0 ? (totalDiscountAmount / (qty * mrp)) * 100 : 0;

      console.log(`[Quotation] Special discount debug:`, {
        product_name: item.description,
        base_discount: baseDiscountNonNegative.toFixed(2),
        special_discount: specialDiscount.toFixed(2),
        total_discount: totalDiscountAmount.toFixed(2),
        scheme: scheme?.target || "none",
      });

      return {
        ...item,
        discountAmount: totalDiscountAmount >= 0 ? totalDiscountAmount : 0,
        specialDiscountAmount: specialDiscount,
        discountPercentage: discountPercentage.toFixed(2),
        discountSchemeType: schemeType || "",
        total: total >= 0 ? total : 0,
      };
    },
    [calculateSpecialDiscount]
  );

  // Recalculate items when discount schemes or products change
  useEffect(() => {
    if (discountSchemes.length > 0 && products.length > 0) {
      setItems((prevItems) =>
        prevItems.map((item) => updateItemTotal(item, quotation.date))
      );
    }
  }, [discountSchemes, products, updateItemTotal, quotation.date]);

  const handleNewItemProductSelect = async (selectedOption) => {
    if (!selectedOption) {
      setNewItem({
        ...newItem,
        productId: null,
        variantId: null,
        description: "",
        mrp: 0,
        sellingPrice: 0,
        discountAmount: 0,
        specialDiscountAmount: 0,
      });
      setErrors((prev) => ({ ...prev, newItemDescription: undefined }));
      return;
    }

    const productId = selectedOption.productId;
    const variantId = selectedOption.variantId;
    const productName = selectedOption.productName; // Clean product name without batch details

    // Find the base product
    const baseProduct = products.find((p) => p.product_id === productId);
    if (!baseProduct) {
      console.warn(`Product not found for productId: ${productId}`);
      return;
    }

    let productData;
    if (variantId && baseProduct.variants && baseProduct.variants.length > 0) {
      // Find the specific variant
      const variant = baseProduct.variants.find(
        (v) => v.product_variant_id === variantId
      );
      if (variant) {
        productData = {
          ...baseProduct,
          ...variant,
          mrp: parseFloat(variant.mrp) || 0,
          sales_price: parseFloat(variant.sales_price) || 0,
        };
      } else {
        console.warn(`Variant not found for variantId: ${variantId}`);
        productData = baseProduct;
      }
    } else {
      // No variant, use base product
      productData = baseProduct;
    }

    const mrp = parseFloat(productData.mrp) || 0;
    const sellingPrice = parseFloat(productData.sales_price) || 0;
    const qty = newItem.qty || 1;

    // Create a temporary item for calculation
    const tempItem = {
      productId,
      variantId,
      qty,
      mrp,
      sellingPrice,
      description: productName,
    };

    // Use updateItemTotal to calculate all values including special discount
    const calculatedItem = updateItemTotal(tempItem, quotation.date);

    setNewItem({
      ...newItem,
      productId,
      variantId,
      description: productName, // Use clean product name only
      mrp,
      sellingPrice,
      discountAmount: calculatedItem.discountAmount,
      specialDiscountAmount: calculatedItem.specialDiscountAmount,
    });
    setErrors((prev) => ({ ...prev, newItemDescription: undefined }));
  };

  const handleNewItemInputChange = (e, field) => {
    const value = e.target.value;
    setNewItem((prev) => ({
      ...prev,
      [field]:
        field === "qty" ||
        field === "mrp" ||
        field === "sellingPrice" ||
        field === "freeQty" ||
        field === "discountAmount"
          ? value === ""
            ? ""
            : parseFloat(value) || 0
          : value,
    }));
    setErrors((prev) => ({
      ...prev,
      [`newItem${field.charAt(0).toUpperCase() + field.slice(1)}`]: undefined,
    }));
  };

  // const handleAddNewItem = async () => {
  //   const {
  //     productId,
  //     description,
  //     qty,
  //     mrp,
  //     sellingPrice,
  //     freeQty,
  //     specialDiscountAmount,
  //   } = newItem;
  //   const newErrors = {};
  //   if (!productId || !description)
  //     newErrors.newItemDescription = "Product is required";
  //   if (qty === "" || qty <= 0)
  //     newErrors.newItemQty = "Quantity must be positive";
  //   if (mrp === "" || mrp < 0)
  //     newErrors.newItemMrp = "MRP must be non-negative";
  //   if (sellingPrice === "" || sellingPrice < 0)
  //     newErrors.newItemSellingPrice = "Selling Price must be non-negative";
  //   if (freeQty < 0)
  //     newErrors.newItemFreeQty = "Free quantity cannot be negative";
  //   if (specialDiscountAmount < 0)
  //     newErrors.newItemSpecialDiscount = "Special discount cannot be negative";

  //   const product = products.find((p) => p.product_id === productId);
  //   if (product && qty > product.opening_stock_quantity) {
  //     newErrors.newItemQty = `Quantity exceeds available stock (${product.opening_stock_quantity})`;
  //   }

  //   if (Object.keys(newErrors).length > 0) {
  //     setErrors((prev) => ({ ...prev, ...newErrors }));
  //     toast.warn("Please fix validation errors.");
  //     return;
  //   }

  //   // Check if item with same productId already exists
  //   const existingItemIndex = items.findIndex(
  //     (item) => item.productId === productId
  //   );

  //   if (existingItemIndex !== -1) {
  //     // Update existing item qty and recalculate totals
  //     const updatedItems = [...items];
  //     const existingItem = updatedItems[existingItemIndex];
  //     const newQty = existingItem.qty + qty;

  //     const { discount: calculatedSpecialDiscount, schemeType } =
  //       calculateSpecialDiscount(
  //         { productId, qty: newQty, mrp },
  //         quotation.date
  //       );

  //     const normalDiscount = mrp - sellingPrice;
  //     const discountAmount =
  //       calculatedSpecialDiscount > 0
  //         ? normalDiscount + calculatedSpecialDiscount
  //         : normalDiscount;

  //     const totalBeforeDiscount = newQty * mrp;
  //     const total = totalBeforeDiscount - discountAmount;
  //     const discountPercentage =
  //       totalBeforeDiscount > 0 ? (discountAmount / (newQty * mrp)) * 100 : 0;

  //     existingItem.qty = newQty;
  //     existingItem.discountAmount = discountAmount;
  //     existingItem.specialDiscountAmount = calculatedSpecialDiscount;
  //     existingItem.discountPercentage = discountPercentage.toFixed(2);
  //     existingItem.discountSchemeType = schemeType || "";
  //     existingItem.total = total;

  //     updatedItems[existingItemIndex] = existingItem;
  //     setItems(updatedItems);
  //   } else {
  //     // Add new item as usual
  //     const { discount: calculatedSpecialDiscount, schemeType } =
  //       calculateSpecialDiscount({ productId, qty, mrp }, quotation.date);

  //     const normalDiscount = mrp - sellingPrice;
  //     const discountAmount =
  //       calculatedSpecialDiscount > 0
  //         ? normalDiscount + calculatedSpecialDiscount
  //         : normalDiscount;

  //     const totalBeforeDiscount = qty * mrp;
  //     const total = totalBeforeDiscount - discountAmount;
  //     const discountPercentage =
  //       totalBeforeDiscount > 0 ? (discountAmount / (qty * mrp)) * 100 : 0;

  //     const newItemToAdd = {
  //       productId,
  //       description,
  //       qty,
  //       mrp,
  //       sellingPrice,
  //       freeQty: freeQty || 0,
  //       discountAmount,
  //       specialDiscountAmount: calculatedSpecialDiscount,
  //       discountPercentage: discountPercentage.toFixed(2),
  //       discountSchemeType: schemeType || "",
  //       total,
  //     };

  //     setItems([...items, newItemToAdd]);
  //   }

  //   setNewItem({
  //     productId: null,
  //     description: "",
  //     qty: 1,
  //     mrp: 0,
  //     sellingPrice: 0,
  //     freeQty: 0,
  //     specialDiscountAmount: 0,
  //     discountAmount: 0,
  //   });
  //   setIsDiscountManuallyEdited(false);
  //   setErrors({});
  //   newItemProductSelectRef.current?.focus();
  // };

  const handleAddNewItem = async () => {
    const {
      productId,
      description,
      qty,
      mrp,
      sellingPrice,
      freeQty,
      specialDiscountAmount,
    } = newItem;
    const newErrors = {};
    if (!productId || !description)
      newErrors.newItemDescription = "Product is required";
    if (qty === "" || qty <= 0)
      newErrors.newItemQty = "Quantity must be positive";
    if (mrp === "" || mrp < 0)
      newErrors.newItemMrp = "MRP must be non-negative";
    if (sellingPrice === "" || sellingPrice < 0)
      newErrors.newItemSellingPrice = "Selling Price must be non-negative";
    if (freeQty < 0)
      newErrors.newItemFreeQty = "Free quantity cannot be negative";
    if (specialDiscountAmount < 0)
      newErrors.newItemSpecialDiscount = "Special discount cannot be negative";

    const product = products.find((p) => p.product_id === productId);
    if (product && qty > product.opening_stock_quantity) {
      newErrors.newItemQty = `Quantity exceeds available stock (${product.opening_stock_quantity})`;
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors((prev) => ({ ...prev, ...newErrors }));
      toast.warn("Please fix validation errors.");
      return;
    }

    // Check if item with same productId AND variantId already exists
    console.log("=== CHECKING FOR EXISTING ITEMS ===");
    console.log("Looking for productId:", productId);
    console.log("Looking for variantId:", newItem.variantId || null);
    console.log(
      "Current items:",
      items.map((item) => ({
        productId: item.productId,
        variantId: item.variantId,
        description: item.description,
        qty: item.qty,
      }))
    );

    const existingItemIndex = items.findIndex((item) => {
      const productMatch = item.productId === productId;
      const variantMatch = item.variantId === (newItem.variantId || null);
      console.log(
        `Item ${item.description}: productMatch=${productMatch}, variantMatch=${variantMatch}`
      );
      return productMatch && variantMatch;
    });

    console.log("Existing item index found:", existingItemIndex);

    if (existingItemIndex !== -1) {
      // Update existing item qty and recalculate totals
      const updatedItems = [...items];
      const existingItem = updatedItems[existingItemIndex];
      const newQty = existingItem.qty + qty;

      // Update existing item quantity
      existingItem.qty = newQty;

      // Recalculate all values using updateItemTotal
      const recalculatedItem = updateItemTotal(existingItem, quotation.date);

      updatedItems[existingItemIndex] = recalculatedItem;
      setItems(updatedItems);
    } else {
      // Add new item as usual
      const newItemToAdd = {
        productId,
        variantId: newItem.variantId || null, // Store variant ID
        description,
        qty,
        mrp,
        sellingPrice,
        freeQty: freeQty || 0,
        discountAmount: 0,
        specialDiscountAmount: 0,
        discountPercentage: "0",
        discountSchemeType: "",
        total: 0,
      };

      // Calculate all values using updateItemTotal
      const calculatedItem = updateItemTotal(newItemToAdd, quotation.date);

      setItems([...items, calculatedItem]);
    }

    setNewItem({
      productId: null,
      variantId: null,
      description: "",
      qty: 1,
      mrp: 0,
      sellingPrice: 0,
      freeQty: 0,
      specialDiscountAmount: 0,
      discountAmount: 0,
    });
    setIsDiscountManuallyEdited(false);
    setErrors({});
    newItemProductSelectRef.current?.focus();
  };

  const handleItemChange = async (index, e) => {
    const { name, value } = e.target;
    const updatedItems = [...items];
    updatedItems[index][name] =
      name === "qty" ||
      name === "mrp" ||
      name === "sellingPrice" ||
      name === "freeQty" ||
      name === "discountAmount"
        ? value === ""
          ? ""
          : parseFloat(value) || 0
        : value;

    const item = updatedItems[index];

    if (name === "qty" || name === "mrp" || name === "sellingPrice") {
      // Recalculate all values using updateItemTotal
      const recalculatedItem = updateItemTotal(item, quotation.date);

      // Update the item with recalculated values
      Object.assign(item, recalculatedItem);
    } else if (name === "discountAmount") {
      // When manually editing discount amount, update total discount
      item.discountAmount = parseFloat(value) || 0;
      item.isDiscountManuallyEdited = true;
      item.total = item.qty * item.mrp - item.discountAmount;
      item.discountPercentage =
        item.qty * item.mrp > 0
          ? (item.discountAmount / (item.qty * item.mrp)) * 100
          : 0;
    } else if (name === "discountPercentage") {
      const totalMrp = item.qty * item.mrp;
      item.discountAmount = (totalMrp * parseFloat(value || 0)) / 100;
      item.isDiscountManuallyEdited = true;
      item.total = item.qty * item.mrp - item.discountAmount;
    }

    setItems(updatedItems);
  };

  const handleFooterChange = (e) => {
    const { name, value } = e.target;
    setFooterDetails({ ...footerDetails, [name]: value });
  };

  const removeItem = (index) => {
    const updatedItems = items.filter((_, idx) => idx !== index);
    setItems(updatedItems);
  };

  const calculateTotal = () => {
    return items.reduce((sum, item) => sum + (item.total || 0), 0);
  };

  const calculateTotalDiscount = () => {
    return items.reduce((sum, item) => sum + (item.discountAmount || 0), 0);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    const newErrors = {};
    if (!customer.id) {
      newErrors.customerName = "Customer is required";
    }
    if (customer.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(customer.email)) {
      newErrors.customerEmail = "Invalid email address";
    }
    if (items.length === 0) {
      newErrors.items = "At least one item is required";
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      toast.error("Please fix validation errors.");
      return;
    }

    if (!user?.token) {
      toast.error("User not authenticated. Please log in.");
      return;
    }

    const quotationData = {
      customer_id: customer.id,
      date: quotation.date,
      time: quotation.time,
      agent_name: agent.name,
      agent_phone: agent.phone,
      tax: tax,
      total: calculateTotal() + tax,
      prepared_by: footerDetails.preparedBy,
      approved_by: footerDetails.approvedBy || "",
      items: items.map((item) => ({
        product_id: item.productId,
        variant_id: item.variantId || null, // Include variantId when saving
        description: item.description,
        qty: item.qty,
        mrp: item.mrp,
        selling_price: item.sellingPrice,
        free_qty: item.freeQty,
        discount_amount: item.discountAmount,
        special_discount_amount: item.specialDiscountAmount,
        discount_percentage: item.discountPercentage,
        discount_scheme_type: item.discountSchemeType || "",
        total: item.total,
      })),
    };

    try {
      console.log("Saving quotation:", JSON.stringify(quotationData, null, 2));
      if (editingQuotationId) {
        const response = await axios.put(
          `http://127.0.0.1:8000/api/quotations/${editingQuotationId}`,
          quotationData,
          { headers: getAuthHeaders() }
        );
        setQuotations(
          quotations.map((q) =>
            q.id === editingQuotationId
              ? {
                  id: response.data.id,
                  customer: {
                    id: response.data.customer.id,
                    name: response.data.customer.customer_name,
                    address: response.data.customer.address || "",
                    phone: response.data.customer.phone || "",
                    email: response.data.customer.email || "",
                  },
                  agent: {
                    name: response.data.agent_name || "",
                    phone: response.data.agent_phone || "",
                  },
                  items: response.data.items.map((item) => ({
                    productId: item.product_id,
                    description: item.description,
                    qty: item.qty,
                    mrp: parseFloat(item.mrp),
                    sellingPrice: parseFloat(item.selling_price),
                    freeQty: item.free_qty,
                    discountAmount: parseFloat(item.discount_amount),
                    specialDiscountAmount: parseFloat(
                      item.special_discount_amount
                    ),
                    discountPercentage: parseFloat(item.discount_percentage),
                    discountSchemeType: item.discount_scheme_type,
                    total: parseFloat(item.total),
                  })),
                  total: parseFloat(response.data.total),
                  date: response.data.date,
                  quotation: {
                    no: response.data.quotation_no,
                    date: response.data.date,
                    time: response.data.time,
                  },
                  footerDetails: {
                    receivedBy: response.data.received_by || "",
                    preparedBy: response.data.prepared_by || "",
                    checkedBy: response.data.checked_by || "",
                    authorizedBy: response.data.authorized_by || "",
                    approved: response.data.approved_by || "",
                    date: response.data.date || "",
                    time: response.data.time || "",
                  },
                }
              : q
          )
        );
        toast.success("Quotation updated successfully!");
      } else {
        const response = await axios.post(
          "http://127.0.0.1:8000/api/quotations",
          quotationData,
          { headers: getAuthHeaders() }
        );
        setQuotations([
          ...quotations,
          {
            id: response.data.id,
            customer: {
              id: response.data.customer.id,
              name: response.data.customer.customer_name,
              address: response.data.customer.address || "",
              phone: response.data.customer.phone || "",
              email: response.data.customer.email || "",
            },
            agent: {
              name: response.data.agent_name || "",
              phone: response.data.agent_phone || "",
            },
            items: response.data.items.map((item) => ({
              productId: item.product_id,
              description: item.description,
              qty: item.qty,
              mrp: parseFloat(item.mrp),
              sellingPrice: parseFloat(item.selling_price),
              freeQty: item.free_qty,
              discountAmount: parseFloat(item.discount_amount),
              specialDiscountAmount: parseFloat(item.special_discount_amount),
              discountPercentage: parseFloat(item.discount_percentage),
              discountSchemeType: item.discount_scheme_type,
              total: parseFloat(item.total),
            })),
            total: parseFloat(response.data.total),
            date: response.data.date,
            quotation: {
              no: response.data.quotation_no,
              date: response.data.date,
              time: response.data.time,
            },
            footerDetails: {
              receivedBy: response.data.received_by || "",
              preparedBy: response.data.prepared_by || "",
              checkedBy: response.data.checked_by || "",
              authorizedBy: response.data.authorized_by || "",
              approved: response.data.approved_by || "",
              date: response.data.date || "",
              time: response.data.time || "",
            },
          },
        ]);
        toast.success("Quotation saved successfully!");
      }

      // Reset form
      setCustomer({ id: null, name: "", address: "", phone: "", email: "" });
      setAgent({ name: "", phone: "" });
      setItems([]);
      setNewItem({
        productId: null,
        description: "",
        qty: 1,
        mrp: 0,
        sellingPrice: 0,
        freeQty: 0,
        specialDiscountAmount: 0,
        discountAmount: 0,
      });
      setIsDiscountManuallyEdited(false);
      setFooterDetails({
        preparedBy: user?.name || "",
        approvedBy: "",
      });
      setQuotation({
        no: "",
        date: new Date().toISOString().split("T")[0],
        time: getSriLankaTime(),
      });
      setErrors({});
      setShowForm(false);
      setEditingQuotationId(null);
    } catch (error) {
      console.error("Error saving quotation:", error);
      if (error.response) {
        console.error("Backend response data:", error.response.data);
        const backendErrors =
          error.response.data.error || error.response.data.errors;
        if (backendErrors) {
          if (typeof backendErrors === "string") {
            toast.error(backendErrors);
          } else if (typeof backendErrors === "object") {
            const messages = Object.values(backendErrors).flat().join(" ");
            toast.error(messages);
          } else {
            toast.error("Failed to save quotation due to backend error.");
          }
        } else {
          toast.error(
            error.response.data.message || "Failed to save quotation."
          );
        }
      } else if (error.request) {
        toast.error("No response from server. Please check your network.");
      } else {
        toast.error("Error saving quotation: " + error.message);
      }
    }
  };

  const handlePrint = () => {
    setShowPrintPreview(true);
    setTimeout(() => {
      const element = printableRef.current;
      const opt = {
        margin: 5,
        filename: `quotation_${quotation.no || "new"}.pdf`,
        image: { type: "jpeg", quality: 0.98 },
        html2canvas: { scale: 2 },
        jsPDF: { unit: "mm", format: "a4", orientation: "portrait" },
      };
      html2pdf()
        .set(opt)
        .from(element)
        .save()
        .then(() => {
          setShowPrintPreviewMinimized(false);
        });
    }, 0);
  };

  const handleKeyDown = (e, ref) => {
    if (e.key !== "Enter") return;
    e.preventDefault();
    const fields = [
      customerNameRef,
      customerAddressRef,
      customerPhoneRef,
      customerEmailRef,
      newItemProductSelectRef,
      newItemQtyRef,
      newItemMrpRef,
      newItemSellingPriceRef,
      newItemFreeQtyRef,
    ];
    const currentIndex = fields.findIndex((field) => field === ref);
    if (currentIndex === fields.length - 1) {
      handleAddNewItem();
    } else {
      const nextField = fields[currentIndex + 1];
      if (nextField.current) {
        nextField.current.focus();
        if (
          nextField !== newItemProductSelectRef &&
          nextField !== customerNameRef
        )
          nextField.current.select?.();
      }
    }
  };

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
  };

  const handleDelete = async (id) => {
    try {
      await axios.delete(`http://127.0.0.1:8000/api/quotations/${id}`, { headers: getAuthHeaders() });
      setQuotations(quotations.filter((q) => q.id !== id));
      toast.success("Quotation deleted successfully!");
    } catch (error) {
      console.error("Error deleting quotation:", error);
      toast.error("Failed to delete quotation.");
    }
  };

  const handleEdit = async (id) => {
    try {
      if (!user?.token) {
        toast.error("User not authenticated. Please log in.");
        return;
      }
      const response = await axios.get(
        `http://127.0.0.1:8000/api/quotations/${id}`,
        { headers: getAuthHeaders() }
      );
      const q = response.data;
      setEditingQuotationId(id);
      setCustomer({
        id: q.customer.id,
        name: q.customer.customer_name,
        address: q.customer.address || "",
        phone: q.customer.phone || "",
        email: q.customer.email || "",
      });
      setAgent({
        name: q.agent_name || "",
        phone: q.agent_phone || "",
      });
      console.log("=== LOADING ITEMS IN EDIT MODE ===");
      console.log("Raw items from API:", q.items);

      const loadedItems = q.items.map((item) => {
        console.log("Processing item:", item);
        return {
          productId: item.product_id,
          variantId: item.variant_id || null, // Include variantId from database
          description: item.description,
          qty: item.qty,
          mrp: parseFloat(item.mrp),
          sellingPrice: parseFloat(item.selling_price),
          freeQty: item.free_qty,
          discountAmount: parseFloat(item.discount_amount),
          specialDiscountAmount: parseFloat(item.special_discount_amount),
          discountPercentage: parseFloat(item.discount_percentage),
          discountSchemeType: item.discount_scheme_type,
          total: parseFloat(item.total),
        };
      });

      console.log("Processed items:", loadedItems);
      setItems(loadedItems);
      setFooterDetails({
        receivedBy: q.received_by || "",
        preparedBy: q.prepared_by || "",
        checkedBy: q.checked_by || "",
        authorizedBy: q.authorized_by || "",
        approvedBy: q.approved_by || "",
        date: q.date || "",
        time: q.time || "",
      });
      setQuotation({
        no: q.quotation_no,
        date: q.date,
        time: q.time,
      });
      setTax(parseFloat(q.tax) || 0);
      setShowForm(true);
    } catch (error) {
      console.error("Error fetching quotation for edit:", error);
      toast.error("Failed to load quotation for editing.");
    }
  };
  const handleView = async (id) => {
    try {
      const response = await axios.get(
        `http://127.0.0.1:8000/api/quotations/${id}`,
        { headers: getAuthHeaders() }
      );
      const q = response.data;
      setCustomer({
        id: q.customer.id,
        name: q.customer.customer_name,
        address: q.customer.address || "",
        phone: q.customer.phone || "",
        email: q.customer.email || "",
      });
      setAgent({
        name: q.agent_name || "",
        phone: q.agent_phone || "",
      });
      setItems(
        q.items.map((item) => ({
          productId: item.product_id,
          variantId: item.variant_id || null, // Include variantId from database
          description: item.description,
          qty: item.qty,
          mrp: parseFloat(item.mrp),
          sellingPrice: parseFloat(item.selling_price),
          freeQty: item.free_qty,
          discountAmount: parseFloat(item.discount_amount),
          specialDiscountAmount: parseFloat(item.special_discount_amount),
          discountPercentage: parseFloat(item.discount_percentage),
          discountSchemeType: item.discount_scheme_type,
          total: parseFloat(item.total),
        }))
      );
      setFooterDetails({
        receivedBy: q.received_by || "",
        preparedBy: q.prepared_by || "",
        checkedBy: q.checked_by || "",
        authorizedBy: q.authorized_by || "",
        approved: q.approved || "",
        date: q.date || "",
        time: q.time || "",
      });
      setQuotation({
        no: q.quotation_no,
        date: q.date,
        time: q.time,
      });
      setTax(parseFloat(q.tax) || 0); // Set tax state from fetched data
      setShowPrintPreview(true);
    } catch (error) {
      console.error("Error fetching quotation for view:", error);
      toast.error("Failed to load quotation for viewing.");
    }
  };

  const toggleRowExpansion = (id) => {
    setExpandedRows((prev) =>
      prev.includes(id) ? prev.filter((rowId) => rowId !== id) : [...prev, id]
    );
  };

  const customerOptions = customers.map((c) => ({
    value: c.id,
    label: c.customer_name,
  }));

  // Generate filtered product options with variants (batch-wise selection)
  // const filteredProductOptions = useMemo(() => {
  //   const options = [];
  //   products.forEach((product) => {
  //     if (product.variants && product.variants.length > 0) {
  //       // Product has variants - create option for each variant
  //       product.variants.forEach((variant) => {
  //         const categoryName = product.category_name || "Unknown Category";
  //         const stockInfo = variant.opening_stock_quantity || 0;
  //         const batchInfo = variant.batch_number
  //           ? ` (Batch: ${variant.batch_number})`
  //           : "";
  //         const expiryInfo = variant.expiry_date
  //           ? ` (Exp: ${variant.expiry_date.split("T")[0]})`
  //           : "";

  //         const searchableText =
  //           `${product.product_name}${batchInfo}${expiryInfo}`.toLowerCase();

  //         if (searchableText.includes(productSearchTerm.toLowerCase())) {
  //           options.push({
  //             value: `${product.product_id}-${variant.product_variant_id}`,
  //             label: `${product.product_name}${batchInfo}${expiryInfo}`,
  //             description: `Stock: ${stockInfo}, Category: ${categoryName}, MRP: LKR ${parseFloat(variant.mrp || 0).toFixed(2)}, Sales Price: LKR ${parseFloat(variant.sales_price || 0).toFixed(2)}`,
  //             productId: product.product_id,
  //             variantId: variant.product_variant_id,
  //             productName: product.product_name, // Store clean product name
  //           });
  //         }
  //       });
  //     } else {
  //       // No variants - add product as is
  //       const searchableText =
  //         `${product.product_name} ${product.description || ""}`.toLowerCase();

  //       if (searchableText.includes(productSearchTerm.toLowerCase())) {
  //         options.push({
  //           value: product.product_id,
  //           label: `${product.product_name} (${product.description || "No description"})`,
  //           description: `Stock: ${product.opening_stock_quantity ?? "N/A"}, Category: ${product.category}, MRP: LKR ${product.mrp.toFixed(2)}, Sales Price: LKR ${product.sales_price.toFixed(2)}`,
  //           productId: product.product_id,
  //           variantId: null,
  //           productName: product.product_name, // Store clean product name
  //         });
  //       }
  //     }
  //   });
  //   return options;
  // }, [products, productSearchTerm]);


  const filteredProductOptions = useMemo(() => {
    const startsWithOnly = localStorage.getItem("productSearchStartsWithOnly") === "true";
    const productSearchTermLower = productSearchTerm.toLowerCase();
    const options = [];
    products.forEach((product) => {
      if (product.variants && product.variants.length > 0) {
        product.variants.forEach((variant) => {
          const batchInfo = variant.batch_number ? ` (Batch: ${variant.batch_number})` : "";
          const expiryInfo = variant.expiry_date ? ` (Exp: ${variant.expiry_date.split("T")[0]})` : "";
          const searchableText = `${product.product_name}${batchInfo}${expiryInfo}`.toLowerCase();
          const match = startsWithOnly
            ? searchableText.startsWith(productSearchTermLower)
            : searchableText.includes(productSearchTermLower);
          if (match) {
            options.push({
              value: `${product.product_id}-${variant.product_variant_id}`,
              label: `${product.product_name}${batchInfo}${expiryInfo}`,
              description: `Stock: ${variant.opening_stock_quantity || 0}, Category: ${product.category || "Unknown"}, MRP: LKR ${parseFloat(variant.mrp || 0).toFixed(2)}, Sales Price: LKR ${parseFloat(variant.sales_price || 0).toFixed(2)}`,
              productId: product.product_id,
              variantId: variant.product_variant_id,
              productName: product.product_name,
            });
          }
        });
      } else {
        const searchableText = `${product.product_name} ${product.description || ""}`.toLowerCase();
        const match = startsWithOnly
          ? searchableText.startsWith(productSearchTermLower)
          : searchableText.includes(productSearchTermLower);
        if (match) {
          options.push({
            value: product.product_id,
            label: `${product.product_name} (${product.description || "No description"})`,
            description: `Stock: ${product.opening_stock_quantity ?? "N/A"}, Category: ${product.category}, MRP: LKR ${product.mrp.toFixed(2)}, Sales Price: LKR ${product.sales_price.toFixed(2)}`,
            productId: product.product_id,
            variantId: null,
            productName: product.product_name,
          });
        }
      }
    });
    return options;
  }, [products, productSearchTerm]);
  const filteredQuotations = quotations.filter(
    (q) =>
      q.customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      q.quotation.no.toLowerCase().includes(searchTerm.toLowerCase()) ||
      q.agent?.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="min-h-screen bg-gray-100 p-6 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto">
        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
          <div className="bg-blue-100 dark:bg-blue-900/30 p-4 rounded-lg shadow">
            <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200">
              Total Quotations
            </h3>
            <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
              {quotations.length}
            </p>
          </div>
          <div className="bg-green-100 dark:bg-green-900/30 p-4 rounded-lg shadow">
            <h3 className="text-lg font-semibold text-green-800 dark:text-green-200">
              Total Revenue
            </h3>
            <p className="text-2xl font-bold text-green-900 dark:text-green-100">
              LKR {quotations.reduce((sum, q) => sum + q.total, 0).toFixed(2)}
            </p>
          </div>
          <div className="bg-yellow-100 dark:bg-yellow-900/30 p-4 rounded-lg shadow">
            <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200">
              Pending Approvals
            </h3>
            <p className="text-2xl font-bold text-yellow-900 dark:text-yellow-100">
              {quotations.filter((q) => q.footerDetails.approved === "").length}
            </p>
          </div>
        </div>

        {/* Create New Quotation Button */}
        <div className="flex justify-end mb-8">
          <button
            onClick={() => {
              setShowForm(true);
              setQuotation({
                no: "",
                date: new Date().toISOString().split("T")[0],
                time: getSriLankaTime(), // "HH:mm" format with Sri Lanka local time
              });
              setEditingQuotationId(null);
            }}
            className="px-6 py-2 bg-blue-600 dark:bg-blue-700 text-white rounded-md hover:bg-blue-700 dark:hover:bg-blue-600 transition-all duration-300 transform hover:scale-105 shadow-md"
          >
            Create New Quotation
          </button>
        </div>

        {/* Search and Filter */}
        <div className="mb-8">
          <input
            type="text"
            placeholder="Search quotations by customer, quotation no, or agent..."
            value={searchTerm}
            onChange={handleSearch}
            className="w-full p-3 border border-gray-300 dark:border-gray-700 rounded-md bg-white dark:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        {/* Quotation List */}
        <div className="mt-8">
          <h3 className="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
            Quotation List
          </h3>
          {filteredQuotations.length === 0 ? (
            <p className="text-gray-600 dark:text-gray-400">
              No quotations found.
            </p>
          ) : (
            <div className="w-full">
              <table className="min-w-full border border-gray-300 dark:border-gray-600 rounded-lg shadow-md bg-white dark:bg-gray-800">
                <thead>
                  <tr className="bg-gradient-to-r from-blue-600 to-blue-700 text-white text-sm uppercase tracking-wide">
                    <th className="px-4 py-2 text-left font-semibold border border-gray-300 dark:border-gray-600 w-12"></th>
                    <th className="px-4 py-2 text-left font-semibold border border-gray-300 dark:border-gray-600">
                      Quotation No
                    </th>
                    <th className="px-4 py-2 text-left font-semibold border border-gray-300 dark:border-gray-600">
                      Date
                    </th>
                    <th className="px-4 py-2 text-left font-semibold border border-gray-300 dark:border-gray-600">
                      Time
                    </th>
                    <th className="px-4 py-2 text-left font-semibold border border-gray-300 dark:border-gray-600">
                      Customer
                    </th>
                    <th className="px-4 py-2 text-left font-semibold border border-gray-300 dark:border-gray-600">
                      Customer Phone
                    </th>
                    <th className="px-4 py-2 text-left font-semibold border border-gray-300 dark:border-gray-600">
                      Agent
                    </th>
                    <th className="px-4 py-2 text-right font-semibold border border-gray-300 dark:border-gray-600">
                      Total Discount (LKR)
                    </th>
                    <th className="px-4 py-2 text-right font-semibold border border-gray-300 dark:border-gray-600">
                      Grand Total (LKR)
                    </th>
                    <th className="px-4 py-2 text-center font-semibold border border-gray-300 dark:border-gray-600">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {filteredQuotations.map((quotation) => (
                    <React.Fragment key={quotation.id}>
                      <tr className="hover:bg-gray-50 dark:hover:bg-gray-700 transition duration-200 border-b border-gray-200 dark:border-gray-700">
                        <td className="px-4 py-2 text-sm text-gray-700 dark:text-gray-200 border border-gray-300 dark:border-gray-600">
                          <button
                            onClick={() => toggleRowExpansion(quotation.id)}
                            className="focus:outline-none"
                          >
                            {expandedRows.includes(quotation.id) ? (
                              <FiChevronUp className="text-gray-700 dark:text-gray-200" />
                            ) : (
                              <FiChevronDown className="text-gray-700 dark:text-gray-200" />
                            )}
                          </button>
                        </td>
                        <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100 border border-gray-300 dark:border-gray-600">
                          {formatQuotationNumber(quotation.quotation.no)}
                        </td>
                        <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100 border border-gray-300 dark:border-gray-600">
                          {quotation.date}
                        </td>
                        <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100 border border-gray-300 dark:border-gray-600">
                          {quotation.quotation.time}
                        </td>
                        <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100 border border-gray-300 dark:border-gray-600">
                          {quotation.customer.name}
                        </td>
                        <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100 border border-gray-300 dark:border-gray-600">
                          {quotation.customer.phone || "N/A"}
                        </td>
                        <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100 border border-gray-300 dark:border-gray-600">
                          {quotation.agent?.name || "N/A"}
                        </td>
                        <td className="px-4 py-2 text-sm text-right text-red-600 dark:text-red-400 font-semibold border border-gray-300 dark:border-gray-600">
                          {quotation.items
                            .reduce(
                              (sum, item) => sum + (item.discountAmount || 0),
                              0
                            )
                            .toFixed(2)}
                        </td>
                        <td className="px-4 py-2 text-sm text-right text-green-700 dark:text-green-400 font-bold border border-gray-300 dark:border-gray-600">
                          {quotation.total.toFixed(2)}
                        </td>
                        <td className="px-4 py-2 text-sm text-center border border-gray-300 dark:border-gray-600">
                          <div className="flex justify-center space-x-2">
                            <button
                              onClick={() => handleView(quotation.id)}
                              className="p-2 bg-blue-600 dark:bg-blue-700 text-white rounded-md hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors"
                              title="View"
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                className="h-5 w-5"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                                />
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M2.458 12C3.732 7.943 7.523 5 12 5c4.477 0 8.268 2.943 9.542 7-1.274 4.057-5.065 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                                />
                              </svg>
                            </button>
                            <button
                              onClick={() => handleEdit(quotation.id)}
                              className="p-2 bg-yellow-600 dark:bg-yellow-700 text-white rounded-md hover:bg-yellow-700 dark:hover:bg-yellow-600 transition-colors"
                              title="Edit"
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                className="h-5 w-5"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M11 5h2m-1 1v12m-4-4h8"
                                />
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M16.293 3.293a1 1 0 011.414 0l2.586 2.586a1 1 0 010 1.414L12 15l-4 1 1-4 7.293-7.293z"
                                />
                              </svg>
                            </button>
                            <button
                              onClick={() => handleDelete(quotation.id)}
                              className="p-2 bg-red-600 dark:bg-red-700 text-white rounded-md hover:bg-red-700 dark:hover:bg-red-600 transition-colors"
                              title="Delete"
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                className="h-5 w-5"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M6 18L18 6M6 6l12 12"
                                />
                              </svg>
                            </button>
                          </div>
                        </td>
                      </tr>
                      {expandedRows.includes(quotation.id) && (
                        <tr>
                          <td
                            colSpan="10"
                            className="px-4 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600"
                          >
                            <div className="p-4 border border-gray-300 dark:border-gray-600 rounded">
                              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">
                                Item Details
                              </h4>
                              <table className="min-w-full border border-gray-300 dark:border-gray-600 rounded">
                                <thead>
                                  <tr className="bg-gray-100 dark:bg-gray-700 border-b border-gray-300 dark:border-gray-600">
                                    <th className="px-2 py-1 text-left text-sm font-medium text-gray-700 dark:text-gray-200 border-r border-gray-300 dark:border-gray-600">
                                      Item Description
                                    </th>
                                    <th className="px-2 py-1 text-left text-sm font-medium text-gray-700 dark:text-gray-200 border-r border-gray-300 dark:border-gray-600">
                                      Quantity
                                    </th>
                                    <th className="px-2 py-1 text-left text-sm font-medium text-gray-700 dark:text-gray-200 border-r border-gray-300 dark:border-gray-600">
                                      MRP (LKR)
                                    </th>
                                    <th className="px-2 py-1 text-left text-sm font-medium text-gray-700 dark:text-gray-200 border-r border-gray-300 dark:border-gray-600">
                                      Discount (LKR)
                                    </th>
                                    <th className="px-2 py-1 text-left text-sm font-medium text-gray-700 dark:text-gray-200 border-r border-gray-300 dark:border-gray-600">
                                      Free Qty
                                    </th>
                                    <th className="px-2 py-1 text-left text-sm font-medium text-gray-700 dark:text-gray-200 border-r border-gray-300 dark:border-gray-600">
                                      Selling Price (LKR)
                                    </th>
                                    <th className="px-2 py-1 text-left text-sm font-medium text-gray-700 dark:text-gray-200">
                                      Total (LKR)
                                    </th>
                                  </tr>
                                </thead>
                                <tbody>
                                  {quotation.items.map((item, index) => (
                                    <tr
                                      key={index}
                                      className="border-b border-gray-300 dark:border-gray-600 last:border-b-0"
                                    >
                                      <td className="px-2 py-1 text-sm text-gray-700 dark:text-gray-200 border-r border-gray-300 dark:border-gray-600">
                                        {item.description}
                                      </td>
                                      <td className="px-2 py-1 text-sm text-gray-700 dark:text-gray-200 border-r border-gray-300 dark:border-gray-600">
                                        {item.qty}
                                      </td>
                                      <td className="px-2 py-1 text-sm text-gray-700 dark:text-gray-200 border-r border-gray-300 dark:border-gray-600">
                                        {item.mrp.toFixed(2)}
                                      </td>
                                      <td className="px-2 py-1 text-sm text-gray-700 dark:text-gray-200 border-r border-gray-300 dark:border-gray-600">
                                        {item.discountAmount.toFixed(2)}
                                      </td>
                                      <td className="px-2 py-1 text-sm text-gray-700 dark:text-gray-200 border-r border-gray-300 dark:border-gray-600">
                                        {item.freeQty || 0}
                                      </td>
                                      <td className="px-2 py-1 text-sm text-gray-700 dark:text-gray-200 border-r border-gray-300 dark:border-gray-600">
                                        {item.sellingPrice.toFixed(2)}
                                      </td>
                                      <td className="px-2 py-1 text-sm text-gray-700 dark:text-gray-200">
                                        {item.total.toFixed(2)}
                                      </td>
                                    </tr>
                                  ))}
                                </tbody>
                              </table>
                            </div>
                          </td>
                        </tr>
                      )}
                    </React.Fragment>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
        {/* Quotation Form Popup */}
        {showForm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center p-4 z-50 overflow-y-auto">
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] overflow-y-auto mt-16 mb-8 animate-fade-in border border-gray-200 dark:border-gray-700">
              <div className="w-full">
                <h3 className="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
                  {editingQuotationId ? "Edit Quotation" : "Create Quotation"}
                </h3>
                <div className="grid grid-cols-1 gap-4 mb-4">
                  {/* Quotation Details */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div>
                      <label className="block font-semibold text-gray-700 dark:text-gray-300">
                        Quotation No:
                      </label>
                      <input
                        type="text"
                        name="no"
                        value={quotation.no}
                        placeholder="Auto-generated on save"
                        readOnly
                        className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-100 dark:bg-gray-600 text-gray-900 dark:text-gray-100"
                      />
                      <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                        Quotation number is automatically generated by the
                        system.
                      </p>
                    </div>
                    <div>
                      <label className="block font-semibold text-gray-700 dark:text-gray-300">
                        Quotation Date:
                      </label>
                      <input
                        type="date"
                        name="date"
                        value={quotation.date}
                        onChange={handleQuotationChange}
                        className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                      />
                    </div>
                    <div>
                      <label className="block font-semibold text-gray-700 dark:text-gray-300">
                        Quotation Time:
                      </label>
                      <input
                        type="time"
                        name="time"
                        value={quotation.time}
                        onChange={handleQuotationChange}
                        className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                      />
                    </div>
                  </div>
                  {/* Customer Details */}
                  <h4 className="font-semibold text-blue-700 dark:text-blue-400">
                    Customer Details
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                    <div>
                      <label
                        htmlFor="customerName"
                        className="block font-semibold text-gray-700 dark:text-gray-300"
                      >
                        Customer Name <span className="text-red-500">*</span>
                      </label>
                      <Select
                        inputId="customerName"
                        ref={customerNameRef}
                        options={customerOptions}
                        value={
                          customer.id
                            ? customerOptions.find(
                                (option) => option.value === customer.id
                              ) || null
                            : null
                        }
                        onChange={handleCustomerSelect}
                        placeholder={
                          customersLoading ? "Loading..." : "Select customer"
                        }
                        isClearable
                        isSearchable
                        isDisabled={customersLoading}
                        styles={customSelectStyles}
                        isDarkMode={document.documentElement.classList.contains(
                          "dark"
                        )}
                        onKeyDown={(e) => handleKeyDown(e, customerNameRef)}
                      />
                      {errors.customerName && (
                        <p className="mt-1 text-xs text-red-600">
                          {errors.customerName}
                        </p>
                      )}
                    </div>
                    <div>
                      <label
                        htmlFor="customerAddress"
                        className="block font-semibold text-gray-700 dark:text-gray-300"
                      >
                        Customer Address
                      </label>
                      <input
                        type="text"
                        id="customerAddress"
                        name="address"
                        ref={customerAddressRef}
                        value={customer.address}
                        onChange={handleCustomerChange}
                        className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                        placeholder="Enter Customer Address"
                        onKeyDown={(e) => handleKeyDown(e, customerAddressRef)}
                      />
                    </div>
                    <div>
                      <label
                        htmlFor="customerPhone"
                        className="block font-semibold text-gray-700 dark:text-gray-300"
                      >
                        Customer Phone
                      </label>
                      <input
                        type="text"
                        id="customerPhone"
                        name="phone"
                        ref={customerPhoneRef}
                        value={customer.phone}
                        onChange={handleCustomerChange}
                        className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                        placeholder="Enter Customer Phone"
                        onKeyDown={(e) => handleKeyDown(e, customerPhoneRef)}
                      />
                    </div>
                    <div>
                      <label
                        htmlFor="customerEmail"
                        className="block font-semibold text-gray-700 dark:text-gray-300"
                      >
                        Customer Email
                      </label>
                      <input
                        type="email"
                        id="customerEmail"
                        name="email"
                        ref={customerEmailRef}
                        value={customer.email}
                        onChange={handleCustomerChange}
                        className={`w-full p-2 border rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 ${
                          errors.customerEmail
                            ? "border-red-500"
                            : "border-gray-300 dark:border-gray-600"
                        }`}
                        placeholder="Enter Customer Email"
                        onKeyDown={(e) => handleKeyDown(e, customerEmailRef)}
                      />
                      {errors.customerEmail && (
                        <p className="mt-1 text-xs text-red-600">
                          {errors.customerEmail}
                        </p>
                      )}
                    </div>
                  </div>
                  {/* Agent Details */}
                  <h4 className="font-semibold text-blue-700 dark:text-blue-400">
                    Staff/Broker Agent Details
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <label className="block font-semibold text-gray-700 dark:text-gray-300">
                        Agent Name:
                      </label>
                      <input
                        type="text"
                        name="name"
                        value={agent.name}
                        onChange={handleAgentChange}
                        className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                        placeholder="Enter Agent Name"
                      />
                    </div>
                    <div>
                      <label className="block font-semibold text-gray-700 dark:text-gray-300">
                        Agent Phone:
                      </label>
                      <input
                        type="text"
                        name="phone"
                        value={agent.phone}
                        onChange={handleAgentChange}
                        className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                        placeholder="Enter Agent Phone"
                      />
                    </div>
                  </div>
                  {/* Add New Item Section */}
                  <div className="p-4 mb-6 bg-white border border-gray-200 rounded-lg shadow-sm">
                    <h4 className="pb-2 mb-4 text-lg font-semibold text-gray-800 border-b border-gray-200">
                      Add New Item
                    </h4>
                    <div className="grid grid-cols-1 gap-4 md:grid-cols-12">
                      <div className="md:col-span-4">
                        <label className="block mb-1 text-sm font-medium text-gray-700 dark:text-gray-300">
                          Product <span className="text-red-500">*</span>
                        </label>
                        <Select
                          ref={newItemProductSelectRef}
                          options={filteredProductOptions}
                          value={
                            newItem.productId
                              ? filteredProductOptions.find((option) => {
                                  // For products with variants, match productId-variantId
                                  if (newItem.variantId) {
                                    return (
                                      option.value ===
                                      `${newItem.productId}-${newItem.variantId}`
                                    );
                                  }
                                  // For products without variants, match productId only
                                  return option.value === newItem.productId;
                                }) || null
                              : null
                          }
                          placeholder="Search or select product"
                          isClearable
                          isSearchable
                          onChange={handleNewItemProductSelect}
                          onInputChange={(value) => setProductSearchTerm(value)}
                          styles={customSelectStyles}
                          isDarkMode={document.documentElement.classList.contains(
                            "dark"
                          )}
                          onKeyDown={handleProductSelectKeyDown}
                        />
                        {errors.newItemDescription && (
                          <p className="mt-1 text-xs text-red-600">
                            {errors.newItemDescription}
                          </p>
                        )}
                      </div>
                      <div className="md:col-span-2">
                        <label className="block mb-1 text-sm font-medium text-gray-700 dark:text-gray-300">
                          Quantity <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="number"
                          min="1"
                          step="1"
                          ref={newItemQtyRef}
                          value={newItem.qty}
                          onChange={(e) => handleNewItemInputChange(e, "qty")}
                          onKeyDown={(e) => handleKeyDown(e, newItemQtyRef)}
                          className={`w-full p-2 border rounded-md focus:ring-blue-500 focus:border-blue-500 ${
                            errors.newItemQty
                              ? "border-red-500"
                              : "border-gray-300"
                          }`}
                        />
                        {errors.newItemQty && (
                          <p className="mt-1 text-xs text-red-600">
                            {errors.newItemQty}
                          </p>
                        )}
                      </div>
                      <div className="md:col-span-2">
                        <label className="block mb-1 text-sm font-medium text-gray-700 dark:text-gray-300">
                          MRP (LKR) <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="number"
                          min="0"
                          step="0.01"
                          ref={newItemMrpRef}
                          value={newItem.mrp}
                          onChange={(e) => handleNewItemInputChange(e, "mrp")}
                          onKeyDown={(e) => handleKeyDown(e, newItemMrpRef)}
                          className={`w-full p-2 border rounded-md focus:ring-blue-500 focus:border-blue-500 ${
                            errors.newItemMrp
                              ? "border-red-500"
                              : "border-gray-300"
                          }`}
                        />
                        {errors.newItemMrp && (
                          <p className="mt-1 text-xs text-red-600">
                            {errors.newItemMrp}
                          </p>
                        )}
                      </div>
                      <div className="md:col-span-2">
                        <label className="block mb-1 text-sm font-medium text-gray-700 dark:text-gray-300">
                          Selling Price (LKR){" "}
                          <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="number"
                          min="0"
                          step="0.01"
                          ref={newItemSellingPriceRef}
                          value={newItem.sellingPrice}
                          onChange={(e) =>
                            handleNewItemInputChange(e, "sellingPrice")
                          }
                          onKeyDown={(e) =>
                            handleKeyDown(e, newItemSellingPriceRef)
                          }
                          className={`w-full p-2 border rounded-md focus:ring-blue-500 focus:border-blue-500 ${
                            errors.newItemSellingPrice
                              ? "border-red-500"
                              : "border-gray-300"
                          }`}
                        />
                        {errors.newItemSellingPrice && (
                          <p className="mt-1 text-xs text-red-600">
                            {errors.newItemSellingPrice}
                          </p>
                        )}
                      </div>
                      <div className="md:col-span-2">
                        <label className="block mb-1 text-sm font-medium text-gray-700 dark:text-gray-300">
                          Free Quantity
                        </label>
                        <input
                          type="number"
                          min="0"
                          step="1"
                          ref={newItemFreeQtyRef}
                          value={newItem.freeQty}
                          onChange={(e) =>
                            handleNewItemInputChange(e, "freeQty")
                          }
                          onKeyDown={(e) => handleKeyDown(e, newItemFreeQtyRef)}
                          className={`w-full p-2 border rounded-md focus:ring-blue-500 focus:border-blue-500 ${
                            errors.newItemFreeQty
                              ? "border-red-500"
                              : "border-gray-300"
                          }`}
                        />
                        {errors.newItemFreeQty && (
                          <p className="mt-1 text-xs text-red-600">
                            {errors.newItemFreeQty}
                          </p>
                        )}
                      </div>
                      <div className="md:col-span-2 flex items-end">
                        <button
                          type="button"
                          onClick={handleAddNewItem}
                          className="w-full h-[42px] px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors flex items-center justify-center"
                        >
                          <svg
                            className="w-4 h-4 mr-2"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                            />
                          </svg>
                          Add
                        </button>
                      </div>
                    </div>
                  </div>
                  Items Table
                  <div className="overflow-x-auto">
                    <table className="w-full mb-4">
                      <thead>
                        <tr className="bg-blue-600 dark:bg-blue-800 text-white">
                          <th className="p-3 text-left">Item Description</th>
                          <th className="p-3 text-center">Qty</th>
                          <th className="p-3 text-center">MRP (LKR)</th>
                          <th className="p-3 text-center">Dis (LKR)</th>
                          <th className="p-3 text-center">Dis (%)</th>
                          <th className="p-3 text-center">Free (Qty)</th>
                          {/* <th className="p-3 text-center">Special Dis (LKR)</th> */}
                          <th className="p-3 text-center">
                            Selling Price (LKR)
                          </th>
                          <th className="p-3 text-center">Total (LKR)</th>
                          <th className="p-3 text-center">Action</th>
                        </tr>
                      </thead>
                      <tbody>
                        {items.map((item, index) => (
                          <tr
                            key={index}
                            className="border-b border-gray-200 dark:border-gray-700 even:bg-gray-50 dark:even:bg-gray-700/50"
                          >
                            <td className="p-2">
                              <input
                                type="text"
                                name="description"
                                value={item.description}
                                onChange={(e) => handleItemChange(index, e)}
                                readOnly
                                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-100 dark:bg-gray-600 text-gray-900 dark:text-gray-100"
                                placeholder="Item Description"
                              />
                            </td>
                            <td className="p-2">
                              <input
                                type="number"
                                name="qty"
                                value={item.qty}
                                onChange={(e) => handleItemChange(index, e)}
                                readOnly
                                className="w-20 p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-100 dark:bg-gray-600 text-gray-900 dark:text-gray-100 text-center"
                                min="1"
                              />
                            </td>
                            <td className="p-2">
                              <input
                                type="number"
                                name="mrp"
                                value={item.mrp}
                                onChange={(e) => handleItemChange(index, e)}
                                readOnly
                                className="w-28 p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-100 dark:bg-gray-600 text-gray-900 dark:text-gray-100 text-right"
                                min="0"
                                step="0.01"
                              />
                            </td>
                            <td className="p-2">
                              <input
                                type="number"
                                name="discountAmount"
                                value={item.discountAmount}
                                onChange={(e) => handleItemChange(index, e)}
                                className="w-24 p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-center"
                                min="0"
                                step="0.01"
                              />
                            </td>
                            <td className="p-2">
                              <input
                                type="number"
                                name="discountPercentage"
                                value={item.discountPercentage}
                                onChange={(e) => handleItemChange(index, e)}
                                className="w-20 p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-center"
                                min="0"
                                max="100"
                                step="0.01"
                              />
                            </td>
                            <td className="p-2">
                              <input
                                type="number"
                                name="freeQty"
                                value={item.freeQty}
                                onChange={(e) => handleItemChange(index, e)}
                                readOnly
                                className="w-16 p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-100 dark:bg-gray-600 text-gray-900 dark:text-gray-100 text-center"
                                min="0"
                              />
                            </td>
                            {/* <td className="p-2">
                              <div className="flex flex-col items-center">
                                <input
                                  type="number"
                                  name="specialDiscountAmount"
                                  value={item.specialDiscountAmount}
                                  readOnly
                                  className="w-24 p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-100 dark:bg-gray-600 text-gray-900 dark:text-gray-100 text-center"
                                  min="0"
                                  step="0.01"
                                />
                                {item.specialDiscountAmount > 0 && (
                                  <div className="text-xs text-green-600 dark:text-green-400 mt-1 text-center">
                                    {item.discountSchemeType === "product"
                                      ? "Product Scheme"
                                      : "Category Scheme"}
                                  </div>
                                )}
                                {item.specialDiscountAmount === 0 && (
                                  <div className="text-xs text-gray-400 mt-1 text-center">
                                    No special discount
                                  </div>
                                )}
                              </div>
                            </td> */}
                            <td className="p-2">
                              <input
                                type="number"
                                name="sellingPrice"
                                value={item.sellingPrice}
                                onChange={(e) => handleItemChange(index, e)}
                                readOnly
                                className="w-28 p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-100 dark:bg-gray-600 text-gray-900 dark:text-gray-100 text-right"
                                min="0"
                                step="0.01"
                              />
                            </td>
                            <td className="p-2 text-right">
                              {item.total.toFixed(2)}
                            </td>
                            <td className="p-2 text-center">
                              <button
                                type="button"
                                onClick={() => removeItem(index)}
                                className="px-3 py-1 bg-red-600 text-white rounded-md hover:bg-red-700"
                              >
                                Remove
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                    {errors.items && (
                      <p className="text-red-600 text-sm">{errors.items}</p>
                    )}
                  </div>
                  {/* Totals */}
                  <div className="flex justify-end mb-4">
                    <div className="text-right">
                      <p className="font-semibold text-gray-700 dark:text-gray-300">
                        Total Discount: LKR{" "}
                        {calculateTotalDiscount().toFixed(2)}
                      </p>
                      <p className="font-semibold text-gray-700 dark:text-gray-300">
                        Tax (LKR):
                        <input
                          type="number"
                          min="0"
                          step="0.01"
                          value={tax}
                          onChange={(e) =>
                            setTax(parseFloat(e.target.value) || 0)
                          }
                          className="ml-2 w-24 p-1 border border-gray-300 rounded-md text-right"
                        />
                      </p>
                      <p className="font-bold text-lg text-gray-800 dark:text-gray-200">
                        Grand Total: LKR {(calculateTotal() + tax).toFixed(2)}
                      </p>
                    </div>
                  </div>
                  {/* Footer Details */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    {!editingQuotationId && (
                      <>
                        <div>
                          <label className="block font-semibold text-gray-700 dark:text-gray-300">
                            Prepared By:
                          </label>
                          <input
                            type="text"
                            name="preparedBy"
                            value={footerDetails.preparedBy}
                            onChange={handleFooterChange}
                            className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                            placeholder="Prepared By"
                          />
                        </div>

                        <div>
                          <label className="block font-semibold text-gray-700 dark:text-gray-300">
                            Approved By:
                          </label>
                          <Select
                            name="approvedBy"
                            value={
                              footerDetails.approvedBy
                                ? {
                                    value: footerDetails.approvedBy,
                                    label: footerDetails.approvedBy,
                                  }
                                : null
                            }
                            onChange={handleApprovedByChange}
                            options={users.map((user) => ({
                              value: user.name,
                              label: user.name,
                            }))}
                            styles={customSelectStyles}
                            isClearable
                            isLoading={isLoadingUsers}
                            isDisabled={isLoadingUsers || !!errorUsers}
                            placeholder={
                              errorUsers
                                ? "Error loading users"
                                : isLoadingUsers
                                  ? "Loading users..."
                                  : "Select Approved By"
                            }
                          />
                          {errorUsers && (
                            <p className="mt-1 text-xs text-red-600">
                              {errorUsers}
                            </p>
                          )}
                        </div>
                      </>
                    )}
                  </div>
                  {/* Form Actions */}
                  <div className="flex justify-end gap-4">
                    <button
                      type="button"
                      onClick={() => {
                        setShowForm(false);
                        setErrors({});
                        setEditingQuotationId(null);
                        setCustomer({
                          id: null,
                          name: "",
                          address: "",
                          phone: "",
                          email: "",
                        });
                        setAgent({ name: "", phone: "" });
                        setItems([]);
                        setNewItem({
                          productId: null,
                          description: "",
                          qty: 1,
                          mrp: 0,
                          sellingPrice: 0,
                          freeQty: 0,
                          specialDiscountAmount: 0,
                          discountAmount: 0,
                        });
                        setFooterDetails({
                          preparedBy: user?.name || "",
                        });
                        setQuotation({
                          no: "",
                          date: new Date().toISOString().split("T")[0],
                          time: new Date().toLocaleTimeString([], {
                            hour: "2-digit",
                            minute: "2-digit",
                          }),
                        });
                      }}
                      className="px-6 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
                    >
                      Cancel
                    </button>
                    <button
                      type="button"
                      onClick={handlePrint}
                      className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                    >
                      Print
                    </button>
                    <button
                      type="submit"
                      onClick={handleSubmit}
                      className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                    >
                      {editingQuotationId
                        ? "Update Quotation"
                        : "Save Quotation"}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Print Preview */}
        {showPrintPreview && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div
              className={`bg-white p-6 rounded-lg shadow-xl w-full max-w-4xl transition-all duration-300 ${
                showPrintPreviewMinimized
                  ? "h-16 overflow-hidden"
                  : "max-h-[80vh] overflow-y-auto"
              }`}
              style={{ minHeight: showPrintPreviewMinimized ? "4rem" : "auto" }}
            >
              <div className="flex justify-between items-center mb-4">
                <div className="flex space-x-2">
                  <button
                    onClick={() =>
                      setShowPrintPreviewMinimized(!showPrintPreviewMinimized)
                    }
                    className="px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                  >
                    {showPrintPreviewMinimized ? "Restore" : "Minimize"}
                  </button>
                  <button
                    onClick={handlePrint}
                    className="px-3 py-1 bg-green-600 text-white rounded-md hover:bg-green-700"
                  >
                    Print
                  </button>
                </div>
                <button
                  onClick={() => {
                    setShowPrintPreview(false);
                    setShowPrintPreviewMinimized(false);
                  }}
                  className="px-3 py-1 bg-gray-600 text-white rounded-md hover:bg-gray-700"
                >
                  Close
                </button>
              </div>
              {!showPrintPreviewMinimized && (
                <PrintableQuotation
                  ref={printableRef}
                  quotation={quotation}
                  customer={customer}
                  agent={agent}
                  items={items}
                  footerDetails={{
                    ...footerDetails,
                    approvedBy: footerDetails.approvedBy || "", // Ensure string is passed
                  }}
                  total={{
                    grandTotal: calculateTotal() + tax,
                    totalDiscount: calculateTotalDiscount(),
                    amountPaid: 0,
                    balanceDue: 0,
                    tax: tax,
                  }}
                />
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Quotation;
