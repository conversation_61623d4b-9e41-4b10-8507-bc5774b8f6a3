import React, { useState, useEffect } from "react";
import { Card } from "../../components/ui/card";
import { Button } from "../../components/ui/button";
import { RefreshCw, ClipboardPaste, Send, Trash2, X } from "lucide-react";
import axios from "axios";

const API_URL = "http://localhost:8000/api/customers";
const TEMPLATE_API_URL = "http://localhost:8000/api/promotion-templates";

export const PromotionTemplate = () => {
  const [customers, setCustomers] = useState([]);
  const [selectedCustomers, setSelectedCustomers] = useState([]);
  const [excelData, setExcelData] = useState([]);
  const [message, setMessage] = useState("");
  const [templateName, setTemplateName] = useState("");
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [showPreview, setShowPreview] = useState(false);
  const [previewMessages, setPreviewMessages] = useState([]);
  const [draggedItem, setDraggedItem] = useState(null);

  const handleClearExcelData = () => {
    setExcelData([]);
    setErrors({ ...errors, excel: "" });
  };

  const fetchCustomers = async () => {
    try {
      setLoading(true);
      const response = await axios.get(API_URL);
      const customerData = Array.isArray(response.data)
        ? response.data
        : response.data.data || [];
      setCustomers(customerData);
      setSelectedCustomers(customerData.map((customer) => customer.id));
      setErrors({});
    } catch (err) {
      console.error("Error fetching customers:", err);
      setErrors({ general: "Error fetching customers" });
      setCustomers([]);
      setSelectedCustomers([]);
    } finally {
      setLoading(false);
    }
  };

  const toggleCustomerSelection = (customerId) => {
    setSelectedCustomers((prev) =>
      prev.includes(customerId)
        ? prev.filter((id) => id !== customerId)
        : [...prev, customerId]
    );
  };

  const toggleSelectAllCustomers = () => {
    if (selectedCustomers.length === customers.length) {
      setSelectedCustomers([]);
    } else {
      setSelectedCustomers(customers.map((customer) => customer.id));
    }
  };

  const handlePasteExcelData = async () => {
    try {
      const clipboardData = await navigator.clipboard.readText();
      if (!clipboardData.trim()) {
        setErrors({ excel: "No data found in clipboard" });
        return;
      }

      const rows = clipboardData.split("\n").filter((row) => row.trim());
      const parsedData = rows
        .map((row) => {
          const [name, phone, address] = row.split("\t");
          return {
            name: name?.trim(),
            phone: phone?.trim(),
            address: address?.trim(),
          };
        })
        .filter((customer) => customer.phone);

      if (parsedData.length === 0) {
        setErrors({ excel: "No valid customer data found in clipboard" });
        return;
      }

      setExcelData(parsedData);
      setErrors({});
    } catch (err) {
      console.error("Error pasting data:", err);
      setErrors({
        excel:
          "Failed to read clipboard data. Make sure you've copied data from Excel first.",
      });
    }
  };

  const generatePreviewMessages = () => {
    const excelRecipients = excelData.map((item) => ({
      phone: item.phone,
      name: item.name,
      address: item.address,
    }));

    const customerRecipients = customers
      .filter((customer) => selectedCustomers.includes(customer.id))
      .map((c) => ({
        phone: c.phone,
        name: c.customer_name,
        address: c.address,
      }));

    const allRecipients = [...excelRecipients, ...customerRecipients];

    return allRecipients.slice(0, 5).map((recipient) => {
      let preview = message;
      if (recipient.name) preview = preview.replace(/{name}/g, recipient.name);
      if (recipient.phone)
        preview = preview.replace(/{phone}/g, recipient.phone);
      return {
        ...recipient,
        preview,
      };
    });
  };

  const handlePreview = () => {
    if (!message.trim()) {
      setErrors({ message: "Message is required" });
      return;
    }

    if (!templateName.trim()) {
      setErrors({ templateName: "Template name is required" });
      return;
    }

    const excelRecipients = excelData.length;
    const customerRecipients = customers.filter((c) =>
      selectedCustomers.includes(c.id)
    ).length;

    if (excelRecipients === 0 && customerRecipients === 0) {
      setErrors({ general: "No recipients found" });
      return;
    }

    setPreviewMessages(generatePreviewMessages());
    setShowPreview(true);
  };

  const saveTemplate = async () => {
    try {
      await axios.post(TEMPLATE_API_URL, {
        name: templateName,
        customer_details: selectedCustomers,
        excel_details: excelData,
        message,
      });
    } catch (err) {
      console.error("Error saving template:", err);
      throw new Error(
        `Failed to save template: ${err.response?.data?.message || err.message}`
      );
    }
  };
  const handleSendPromotion = async () => {
    setShowPreview(false);
    setLoading(true);
    setErrors({});

    try {
      const excelRecipients = excelData.map((item) => ({
        phone: item.phone,
        name: item.name,
        address: item.address,
      }));

      const customerRecipients = customers
        .filter((customer) => selectedCustomers.includes(customer.id))
        .map((c) => ({
          phone: c.phone,
          name: c.customer_name,
          address: c.address,
        }));

      const allRecipients = [...excelRecipients, ...customerRecipients];

      if (allRecipients.length === 0) {
        throw new Error("No valid recipients found");
      }

      // Build messages array from actual recipients and message, removing leading + from phone numbers
      const messagesToSend = allRecipients.map((recipient, index) => {
        let phone = recipient.phone;
        if (phone && phone.startsWith("+")) {
          phone = phone.substring(1);
        }
        return {
          clientRef: `promo_${Date.now()}_${index}`,
          number: phone,
          mask: "IMSS.lk",
          text: message
            .replace(/{name}/g, recipient.name || "")
            .replace(/{phone}/g, phone || ""),
          campaignName: templateName && templateName.trim()
            ? templateName.substring(0, 45)
            : `Promo_${Date.now()}_${Math.floor(Math.random() * 10000)}`
        };
      });

      // Log phone numbers and messages for verification
      console.log("Messages to send:", messagesToSend);

      const fetchResponse = await fetch("http://localhost:8000/api/send-dialog-sms", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ messages: messagesToSend }),
      });
      const responseData = await fetchResponse.json();

      if (!responseData.success) {
        throw new Error(responseData.error || "Failed to send SMS");
      }

      alert(
        `Promotion sent successfully to ${allRecipients.length} customers!`
      );
      // resetForm(); // Commented out since not needed for SMS and function is not defined
    } catch (err) {
      console.error("Error:", err);

      let errorMessage = "Failed to send promotions";
      if (err.response) {
        if (err.response.status === 401) {
          errorMessage = "Authentication failed. Please check API credentials.";
        } else {
          errorMessage =
            err.response.data?.error ||
            err.response.data?.message ||
            `Server error (${err.response.status})`;
        }
      } else if (err.message.includes("timeout")) {
        errorMessage = "Request timed out. Please try again.";
      } else if (err.message.includes("Network Error")) {
        errorMessage = "Network error. Please check your connection.";
      } else {
        errorMessage = err.message || errorMessage;
      }

      setErrors({ general: errorMessage });
    } finally {
      setLoading(false);
    }
  };

  const handleDragStart = (e, placeholder) => {
    setDraggedItem(placeholder);
    e.dataTransfer.setData("text/plain", placeholder);
    e.dataTransfer.effectAllowed = "copy";
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "copy";
  };

  const handleDrop = (e) => {
    e.preventDefault();
    const textArea = e.target;
    const startPos = textArea.selectionStart;
    const endPos = textArea.selectionEnd;
    const currentValue = textArea.value;

    setMessage(
      currentValue.substring(0, startPos) +
        `{${draggedItem}}` +
        currentValue.substring(endPos)
    );

    setTimeout(() => {
      textArea.selectionStart = startPos + draggedItem.length + 2;
      textArea.selectionEnd = startPos + draggedItem.length + 2;
    }, 0);
  };

  return (
    <div className="min-h-screen p-6 bg-gray-50">
      <h2 className="mb-6 text-2xl font-bold text-gray-800">
        Promotion Template
      </h2>

      <div className="mb-6">
        <label
          htmlFor="template-name"
          className="block mb-1 text-sm font-medium text-gray-700"
        >
          Template Name *
        </label>
        <input
          id="template-name"
          value={templateName}
          onChange={(e) => {
            setTemplateName(e.target.value);
            setErrors({ ...errors, templateName: "" });
          }}
          className={`w-full p-3 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
            errors.templateName ? "border-red-500" : "border-gray-300"
          }`}
          placeholder="Enter template name..."
        />
        {errors.templateName && (
          <p className="mt-1 text-xs text-red-600">{errors.templateName}</p>
        )}
      </div>

      <div className="grid grid-cols-1 gap-6 mb-6 lg:grid-cols-3">
        <Card className="p-6 bg-white border border-gray-200 rounded-lg shadow-sm">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-700">
              Customer Details
            </h3>
            <Button
              variant="outline"
              onClick={fetchCustomers}
              disabled={loading}
              className="flex items-center gap-2 px-4 py-2 text-white transition-all duration-300 rounded-md shadow-md bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 hover:shadow-lg active:scale-95"
            >
              {loading ? (
                <svg className="w-4 h-4 animate-spin" viewBox="0 0 24 24">
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
              ) : (
                <RefreshCw className="w-4 h-4" />
              )}
              Refresh
            </Button>
          </div>

          {loading && customers.length === 0 ? (
            <div className="py-4 text-center">
              <div className="w-8 h-8 mx-auto border-b-2 border-blue-500 rounded-full animate-spin"></div>
              <p className="mt-2 text-sm text-gray-600">Loading customers...</p>
            </div>
          ) : errors.general ? (
            <div className="p-3 border border-red-200 rounded-md bg-red-50">
              <p className="text-red-700">{errors.general}</p>
            </div>
          ) : customers.length > 0 ? (
            <div className="overflow-auto max-h-96">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-2 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                      <input
                        type="checkbox"
                        checked={
                          selectedCustomers.length === customers.length &&
                          customers.length > 0
                        }
                        onChange={toggleSelectAllCustomers}
                        className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                      />
                    </th>
                    <th className="px-4 py-2 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                      Name
                    </th>
                    <th className="px-4 py-2 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                      Phone
                    </th>
                    <th className="px-4 py-2 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                      Address
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {customers.slice(0, 100).map((customer, index) => (
                    <tr key={index}>
                      <td className="px-4 py-2 text-sm text-gray-900 whitespace-nowrap">
                        <input
                          type="checkbox"
                          checked={selectedCustomers.includes(customer.id)}
                          onChange={() => toggleCustomerSelection(customer.id)}
                          className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                        />
                      </td>
                      <td className="px-4 py-2 text-sm text-gray-900 whitespace-nowrap">
                        {customer.customer_name}
                      </td>
                      <td className="px-4 py-2 text-sm text-gray-500 whitespace-nowrap">
                        {customer.phone}
                      </td>
                      <td className="px-4 py-2 text-sm text-gray-500 whitespace-nowrap">
                        {customer.address || "-"}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              {customers.length > 100 && (
                <p className="mt-2 text-xs text-gray-500">
                  Showing first 100 of {customers.length} customers
                </p>
              )}
              <div className="mt-2 text-sm text-gray-600">
                Selected: {selectedCustomers.length} of {customers.length}{" "}
                customers
              </div>
            </div>
          ) : (
            <p className="py-4 text-center text-gray-500">
              No customer data available. Click Refresh to load customers.
            </p>
          )}
        </Card>

        <Card className="p-6 bg-white border border-gray-200 rounded-lg shadow-sm">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-700">
              Excel Details
            </h3>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={handlePasteExcelData}
                disabled={loading}
                className="flex items-center gap-2 px-4 py-2 text-white transition-all duration-300 rounded-md shadow-md bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 hover:shadow-lg active:scale-95"
              >
                <ClipboardPaste className="w-4 h-4" />
                Paste
              </Button>
              <Button
                variant="outline"
                onClick={handleClearExcelData}
                disabled={loading || excelData.length === 0}
                className="flex items-center gap-2 px-4 py-2 text-white transition-all duration-300 rounded-md shadow-md bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 hover:shadow-lg active:scale-95"
              >
                <Trash2 className="w-4 h-4" />
                Clear
              </Button>
            </div>
          </div>

          {errors.excel ? (
            <div className="p-3 border border-red-200 rounded-md bg-red-50">
              <p className="text-red-700">{errors.excel}</p>
            </div>
          ) : excelData.length > 0 ? (
            <div className="overflow-auto max-h-96">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-2 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                      Name
                    </th>
                    <th className="px-4 py-2 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                      Phone
                    </th>
                    <th className="px-4 py-2 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                      Address
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {excelData.slice(0, 100).map((customer, index) => (
                    <tr key={index}>
                      <td className="px-4 py-2 text-sm text-gray-900 whitespace-nowrap">
                        {customer.name || "-"}
                      </td>
                      <td className="px-4 py-2 text-sm text-gray-500 whitespace-nowrap">
                        {customer.phone}
                      </td>
                      <td className="px-4 py-2 text-sm text-gray-500 whitespace-nowrap">
                        {customer.address || "-"}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              {excelData.length > 100 && (
                <p className="mt-2 text-xs text-gray-500">
                  Showing first 100 of {excelData.length} entries
                </p>
              )}
            </div>
          ) : (
            <div className="py-4 text-center">
              <p className="mb-3 text-gray-500">
                Copy data from Excel (columns: Name, Phone, Address) and click
                Paste
              </p>
              <p className="text-xs text-gray-400">
                Note: Data should be tab-separated as in Excel
              </p>
            </div>
          )}
        </Card>

        <Card className="p-6 bg-white border border-gray-200 rounded-lg shadow-sm">
          <h3 className="mb-4 text-lg font-semibold text-gray-700">Message</h3>

          <div className="mb-4">
            <label
              htmlFor="promotion-message"
              className="block mb-1 text-sm font-medium text-gray-700"
            >
              Promotion Message *
            </label>
            <textarea
              id="promotion-message"
              value={message}
              onChange={(e) => {
                setMessage(e.target.value);
                setErrors({ ...errors, message: "" });
              }}
              onDragOver={handleDragOver}
              onDrop={handleDrop}
              rows={8}
              className={`w-full p-3 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                errors.message ? "border-red-500" : "border-gray-300"
              }`}
              placeholder="Write your promotion message here..."
            />
            {errors.message && (
              <p className="mt-1 text-xs text-red-600">{errors.message}</p>
            )}
          </div>

          <div className="text-xs text-gray-500">
            <p className="mb-2">
              Drag and drop placeholders into your message:
            </p>
            <div className="flex gap-2">
              <div
                draggable
                onDragStart={(e) => handleDragStart(e, "name")}
                className="px-3 py-1 text-sm border border-gray-300 rounded-md cursor-grab hover:bg-gray-50 active:cursor-grabbing"
              >
                {`{name}`}
              </div>
              <div
                draggable
                onDragStart={(e) => handleDragStart(e, "phone")}
                className="px-3 py-1 text-sm border border-gray-300 rounded-md cursor-grab hover:bg-gray-50 active:cursor-grabbing"
              >
                {`{phone}`}
              </div>
            </div>
            <p className="mt-2">Placeholders will be replaced with:</p>
            <ul className="pl-5 mt-1 list-disc">
              <li>{`{name} - Customer's name`}</li>
              <li>{`{phone} - Customer's phone number`}</li>
            </ul>
          </div>
        </Card>
      </div>

      <div className="flex justify-center">
        <Button
          onClick={handlePreview}
          disabled={
            loading ||
            !message.trim() ||
            !templateName.trim() ||
            (selectedCustomers.length === 0 && excelData.length === 0)
          }
          className="flex items-center gap-2 px-8 py-3 text-white transition-all duration-300 rounded-md shadow-lg bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 hover:shadow-xl active:scale-95 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
        >
          {loading ? (
            <>
              <svg
                className="w-5 h-5 text-white animate-spin"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              Sending...
            </>
          ) : (
            <>
              <Send className="w-5 h-5" />
              Preview & Send Promotion
            </>
          )}
        </Button>
      </div>

      {errors.general && (
        <div className="p-3 mt-4 text-center border border-red-200 rounded-md bg-red-50">
          <p className="text-red-700">{errors.general}</p>
        </div>
      )}

      {showPreview && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50">
          <div className="w-full max-w-3xl overflow-hidden bg-white rounded-lg shadow-xl">
            <div className="flex items-center justify-between p-4 border-b">
              <h3 className="text-xl font-bold text-gray-800">
                Message Preview
              </h3>
              <button
                onClick={() => setShowPreview(false)}
                className="p-1 text-gray-400 transition-colors duration-200 rounded-full hover:text-gray-500 hover:bg-gray-100"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            <div className="p-4 overflow-y-auto max-h-[60vh]">
              <h4 className="mb-2 font-medium text-gray-700">
                Sample messages (first 5 recipients):
              </h4>
              <div className="space-y-3">
                {previewMessages.map((recipient, index) => (
                  <div
                    key={index}
                    className="p-3 border border-gray-200 rounded-md"
                  >
                    <div className="mb-2">
                      <span className="font-medium">To: </span>
                      {recipient.name
                        ? `${recipient.name} (${recipient.phone})`
                        : recipient.phone}
                    </div>
                    <div className="p-3 text-sm whitespace-pre-wrap rounded bg-gray-50">
                      {recipient.preview}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex flex-col justify-end gap-3 p-4 border-t sm:flex-row">
              <Button
                onClick={() => setShowPreview(false)}
                className="w-full px-6 py-2 text-white transition-colors duration-200 bg-red-500 border border-red-600 rounded-md sm:w-auto hover:bg-red-600"
              >
                Cancel
              </Button>
              <Button
                onClick={handleSendPromotion}
                disabled={loading}
                className="w-full px-6 py-2 text-white bg-purple-600 rounded-md sm:w-auto hover:bg-purple-700"
              >
                {loading ? (
                  <>
                    <svg
                      className="w-4 h-4 mr-2 text-white animate-spin"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    Sending...
                  </>
                ) : (
                  "Confirm & Send"
                )}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
