import React, { useRef, useState, useEffect } from "react";
import axios from "axios";
import { useRegister } from "../../context/RegisterContext";
import { useAuth } from "../../context/NewAuthContext";
import logo from "./rs_logo.jpg";
import html2canvas from "html2canvas";
import { jsPDF } from "jspdf";
import PrintView from "../print/PrintView";

const BillPrintModal = ({
  initialProducts = [],
  initialBillDiscount = 0,
  initialTax = 0,
  initialShipping = 0,
  initialTotals = {},
  grandTotal = 0,
  unit = "",
  totalItemDiscount = 0,
  initialCustomerInfo = { name: "", mobile: "", bill_number: "", userId: "" },
  saleType = "Retail",
  onClose,
  paidAmount = 0,
  balanceAmountProp = 0,
  billTime = null,
  paymentType: initialPaymentType = "cash", // Add payment type prop
  saleId = null, // For update mode
  isUpdateMode = false, // Flag to indicate update mode
  activeSchemes = [],
}) => {
  const { getAuthHeaders } = useRegister();
  const { user } = useAuth();
  const [billNumber, setBillNumber] = useState(initialCustomerInfo.bill_number);
  const printRef = useRef(null);
  const [receivedAmount, setReceivedAmount] = useState(paidAmount);
  const [balanceAmount, setBalanceAmount] = useState(balanceAmountProp);
  const [customers, setCustomers] = useState([]);
  const [selectedCustomer, setSelectedCustomer] = useState(initialCustomerInfo);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [showMessagePreview, setShowMessagePreview] = useState(false);
  const [messageContent, setMessageContent] = useState("");
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [paymentType, setPaymentType] = useState(initialPaymentType);
  const [chequeNo, setChequeNo] = useState("");
  const [bankName, setBankName] = useState("");
  const [issueDate, setIssueDate] = useState("");
  const [bankAccounts, setBankAccounts] = useState([]);
  const [selectedBankAccount, setSelectedBankAccount] = useState("");
  const [bankAccountSearch, setBankAccountSearch] = useState("");
  const [showBankAccountDropdown, setShowBankAccountDropdown] = useState(false);
  const [companyDetails, setCompanyDetails] = useState({
      company_name: "ABEE WHITE",
      business_address: "NO.30/1, THEATER/GERMAN SCHOOL ROAD,   NINTAVUR-09",
      contact_number: "********** | **********",
      email: "<EMAIL>"
  });
  const [defaultTemplate, setDefaultTemplate] = useState(null);
  const [error, setError] = useState(null);
  const [receivedAmountError, setReceivedAmountError] = useState("");
  const [redeemAmount, setRedeemAmount] = useState(0);

  // Added products state to allow editing discounts
  const [products, setProducts] = useState(() =>
    initialProducts.map((p) => ({
      ...p,
    }))
  );

  // Customer creation state
  const [showAddCustomerForm, setShowAddCustomerForm] = useState(false);
  const [newCustomer, setNewCustomer] = useState({
    name: "",
    mobile: "",
    nic_number: "",
    is_credit_customer: false,
  });
  const [customerErrors, setCustomerErrors] = useState({});

  // Refs for focus management
  const customerSelectRef = useRef(null);
  const receivedAmountRef = useRef(null);
  const saveButtonRef = useRef(null);
  const printButtonRef = useRef(null);
  const saveOnlyButtonRef = useRef(null);
  const newCustomerNameRef = useRef(null);
  const messageContentRef = useRef(null);
  const paymentTypeRef = useRef(null);
  const chequeNoRef = useRef(null);
  const bankNameRef = useRef(null);
  const issueDateRef = useRef(null);
  const chequeRef = useRef(null);
  const bankAccountRef = useRef(null);

  // Default POS Template (static fallback, matching original print design)
  const defaultPOSTemplate = {
    name: "Default POS",
    type: "POS",
    width: 80,
    height: 297,
    text_elements: [
      "MUNSI TEX",
      "Mosque Building, Police Road, Kalmunai",
      "Mob: **********, **********, **********",
      "Thank You! Visit Again.",
      "System by IMSS",
      "visit🔗: www.imss.lk",
    ],
    text_positions: [
      { x: 10, y: 50 },
      { x: 10, y: 70 },
      { x: 10, y: 80 },
      { x: 10, y: 260 },
      { x: 10, y: 270 },
      { x: 10, y: 280 },
    ],
    text_styles: [
      {
        fontSize: 20,
        color: "#000000",
        fontWeight: "bold",
        textAlign: "center",
        fontFamily: "Cressida, Elephant, cursive",
      },
      {
        fontSize: 13,
        color: "#000000",
        fontWeight: "normal",
        textAlign: "center",
        fontFamily: "calibri, sans-serif",
      },
      {
        fontSize: 12,
        color: "#000000",
        fontWeight: "normal",
        textAlign: "center",
        fontFamily: "calibri, sans-serif",
      },
      {
        fontSize: 12,
        color: "#000000",
        fontWeight: "bold",
        textAlign: "center",
        fontFamily: "calibri, sans-serif",
      },
      {
        fontSize: 8,
        color: "#000000",
        fontWeight: "normal",
        textAlign: "center",
        fontFamily: "calibri, sans-serif",
      },
      {
        fontSize: 9,
        color: "#000000",
        fontWeight: "normal",
        textAlign: "center",
        fontFamily: "calibri, sans-serif",
      },
    ],
    text_sizes: [
      { width: 60, height: 20 },
      { width: 60, height: 15 },
      { width: 60, height: 15 },
      { width: 60, height: 15 },
      { width: 60, height: 10 },
      { width: 60, height: 10 },
    ],
    image_elements: [logo],
    image_positions: [{ x: 10, y: 10 }],
    image_sizes: [{ width: 60, height: 40 }],
    placeholder_elements: [
      "c1", // Customer Name
      "t3", // Bill Number
      "t2", // Date
      "t5", // Payment Type
      "t8", // Item List
      "t9", // Subtotal
      "t11", // Discount
      "t12", // Grand Total
      "t13", // Paid
      "t14", // Balance
      "t15", // Terms and Conditions
    ],
    placeholder_positions: [
      { x: 10, y: 100 },
      { x: 50, y: 100 },
      { x: 10, y: 110 },
      { x: 50, y: 110 },
      { x: 10, y: 130 },
      { x: 10, y: 200 },
      { x: 10, y: 210 },
      { x: 10, y: 220 },
      { x: 10, y: 230 },
      { x: 10, y: 240 },
      { x: 10, y: 250 },
    ],
    placeholder_sizes: [
      { width: 30, height: 10 },
      { width: 30, height: 10 },
      { width: 30, height: 10 },
      { width: 30, height: 10 },
      { width: 60, height: 50 },
      { width: 60, height: 10 },
      { width: 60, height: 10 },
      { width: 60, height: 10 },
      { width: 60, height: 10 },
      { width: 60, height: 10 },
      { width: 60, height: 20 },
    ],
    item_list_columns: [
      { id: "no", label: "No", visible: true },
      { id: "name", label: "Name", visible: true },
      { id: "price", label: "Price", visible: true },
      { id: "discount", label: "Dis", visible: true },
      { id: "total", label: "Total", visible: true },
    ],
  };

  // Fetch customers, company details, and default template
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch customers
        const customersResponse = await axios.get(
          "http://127.0.0.1:8000/api/customers",
          getAuthHeaders()
        );
        const customersList = customersResponse.data.data || [];
        setCustomers(customersList);

        // If we have a customer ID in initialCustomerInfo, find and select that customer
        if (initialCustomerInfo.id) {
          const existingCustomer = customersList.find(
            (cust) => cust.id == initialCustomerInfo.id
          );
          if (existingCustomer) {
            setSelectedCustomer({
              id: existingCustomer.id,
              name: existingCustomer.customer_name,
              mobile: existingCustomer.phone,
              is_credit_customer: existingCustomer.is_credit_customer,
              bill_number: billNumber,
            });
          }
        }

        // Fetch company details
        try {
          const companyResponse = await axios.get(
            "http://127.0.0.1:8000/api/company-details",
            getAuthHeaders()
          );
          if (companyResponse.data) {
            setCompanyDetails({
              company_name:
                companyResponse.data.company_name ||
                companyDetails.company_name,
              business_address:
                companyResponse.data.business_address ||
                companyDetails.business_address,
              contact_number:
                companyResponse.data.contact_number ||
                companyDetails.contact_number,
              email:
                companyResponse.data.email ||
                companyDetails.email,
            });
          }
        } catch (companyError) {
          console.warn("Using default company details:", companyError.message);
        }

        // Fetch default invoice template
        try {
          const templatesResponse = await axios.get(
            "http://127.0.0.1:8000/api/invoice-templates",
            getAuthHeaders()
          );
          const defaultInvoiceTemplate = templatesResponse.data.data.find(
            (template) => template.is_default === 1
          );
          if (defaultInvoiceTemplate) {
            setDefaultTemplate(defaultInvoiceTemplate);
            setError(null);
          } else {
            console.warn("No default invoice template found.");
            setError(
              "No default invoice template set. Using fallback template."
            );
            setDefaultTemplate(null);
          }
        } catch (templateError) {
          console.error("Invoice template fetch error:", templateError);
          setError(
            `Failed to fetch invoice template: ${templateError.message}. Using fallback template.`
          );
          setDefaultTemplate(null);
        }

        // Fetch default sales template
        const salesTemplatesResponse = await axios.get(
          "http://127.0.0.1:8000/api/sales-templates",
          getAuthHeaders()
        );
        const defaultSalesTemplate = salesTemplatesResponse.data.data.find(
          (template) => template.is_default
        );
        if (defaultSalesTemplate) {
          setMessageContent(replacePlaceholders(defaultSalesTemplate.content));
        }

        // Fetch bank accounts
        try {
          const bankAccountsResponse = await axios.get(
            "http://127.0.0.1:8000/api/staff-ledger/bank-account-subgroups",
            getAuthHeaders()
          );
          setBankAccounts(bankAccountsResponse.data.data || []);
        } catch (bankError) {
          console.error("Error fetching bank accounts:", bankError);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        setError(
          `Failed to fetch data: ${error.message}. Using fallback template.`
        );
      }
    };

    fetchData();
  }, [
    getAuthHeaders,
    billNumber,
    initialTotals.finalTotal,
    companyDetails.company_name,
    initialCustomerInfo.id, // Add this dependency to re-run when customer ID changes
  ]);

  // Focus management
  useEffect(() => {
    if (customerSelectRef.current) {
      customerSelectRef.current.focus();
    }
  }, []);

  useEffect(() => {
    if (showConfirmation && printButtonRef.current) {
      printButtonRef.current.focus();
    }
  }, [showConfirmation]);

  useEffect(() => {
    if (showMessagePreview && messageContentRef.current) {
      messageContentRef.current.focus();
    }
  }, [showMessagePreview]);

  const totals = initialTotals || {};

  // Handle customer selection
  const handleCustomerChange = async (e) => {
    const id = e.target.value;
    const customer = customers.find((cust) => cust.id == id);
    let newCustomer;

    if (customer) {
      // Fetch loyalty points for this customer
      try {
        const res = await axios.get("http://localhost:8000/api/loyalty-points");
        console.log('Loyalty points API data:', res.data.data, 'Selected ID:', id);
        const loyaltyData = res.data.data.find((c) => String(c.id) === String(id)); // ensure string comparison
        newCustomer = {
          ...customer,
          name: customer.customer_name,
          mobile: customer.phone,
          is_credit_customer: customer.is_credit_customer,
          points_balance: loyaltyData ? loyaltyData.points_balance : 0,
          points_earned: loyaltyData ? loyaltyData.points_earned : 0,
          points_redeemed: loyaltyData ? loyaltyData.points_redeemed : 0,
        };
      } catch (err) {
        newCustomer = {
          ...customer,
          name: customer.customer_name,
          mobile: customer.phone,
          is_credit_customer: customer.is_credit_customer,
          points_balance: 0,
          points_earned: 0,
          points_redeemed: 0,
        };
      }
    } else {
      newCustomer = {
        name: "Walk-in Customer",
        mobile: "",
        bill_number: billNumber,
        is_credit_customer: false,
        points_balance: 0,
        points_earned: 0,
        points_redeemed: 0,
      };
    }

    setSelectedCustomer(newCustomer);

    // Update message content with new customer info if we have a template
    if (messageContent) {
      try {
        const templatesResponse = await axios.get(
          "http://127.0.0.1:8000/api/sales-templates",
          getAuthHeaders()
        );
        const defaultTemplate = templatesResponse.data.data.find(
          (template) => template.is_default
        );
        if (defaultTemplate) {
          setMessageContent(replacePlaceholders(defaultTemplate.content, newCustomer));
        }
      } catch (error) {
        console.error("Error updating message content:", error);
      }
    }
  };

  // Handle received amount change
  const handleReceivedAmountChange = (e) => {
    const amount = parseFloat(e.target.value) || 0;
    setReceivedAmount(amount);
    setBalanceAmount(amount - totals.finalTotal);
  };

  // Update receivedAmount and balanceAmount if props change
  React.useEffect(() => {
    setReceivedAmount(paidAmount);
  }, [paidAmount]);

  React.useEffect(() => {
    setBalanceAmount(balanceAmountProp);
  }, [balanceAmountProp]);

  // Update balance calculation to include redeemAmount
  useEffect(() => {
    setBalanceAmount((receivedAmount + redeemAmount) - (totals.finalTotal || 0));
  }, [receivedAmount, redeemAmount, totals.finalTotal]);

  // Format currency without commas
  const formatCurrency = (amount) => {
    const numAmount = parseFloat(amount || 0);
    if (isNaN(numAmount)) return "LKR 0.00";
    return `LKR ${numAmount.toFixed(2)}`;
  };

  // Function to replace all placeholders in message content
  const replacePlaceholders = (content, customer = selectedCustomer) => {
    if (!content) return "";

    const billUrl = `${window.location.origin}/api/bills/${billNumber}/view`;
    const currentDate = new Date();

    return content
      .replace(/\{\{customer_name\}\}/g, customer?.name || "Walk-in Customer")
      .replace(/\{\{bill_number\}\}/g, billNumber || "")
      .replace(/\{\{bill_date\}\}/g, currentDate.toLocaleDateString())
      .replace(/\{\{bill_amount\}\}/g, formatCurrency(initialTotals.finalTotal))
      .replace(/\{\{bill_url\}\}/g, billUrl)
      .replace(/\{\{company_name\}\}/g, companyDetails.company_name || "")
      .replace(/\{\{bill_time\}\}/g, currentDate.toLocaleTimeString())
      .replace(/\{\{cashier_name\}\}/g, user?.username || "System");
  };

  // Handle saving the bill
  const handleSave = async () => {
    setReceivedAmountError("");
    if (paymentType === "cheque" && receivedAmount > (totals.finalTotal || grandTotal)) {
      setReceivedAmountError("for cheque payments, received amount cannot be greater than the grand total.");
      alert("For cheque payments, received amount cannot be greater than the grand total.");
      return;
    }
    if (redeemAmount > (selectedCustomer?.points_balance || 0)) {
      setReceivedAmountError("Redeem amount cannot exceed available loyalty points.");
      alert("Redeem amount cannot exceed available loyalty points.");
      return;
    }
    if (paymentType === "credit") {
      if (!selectedCustomer?.id) {
        alert("Please select a customer for credit sales.");
        return;
      }
    }

    const billData = {
      bill_number: billNumber,
      customer_id: selectedCustomer?.id || null,
      customer_name: selectedCustomer?.name || "Walk-in Customer",
      subtotal: parseFloat((totals.subTotal || 0).toFixed(2)),
      discount: parseFloat(
        (
          (totals.totalItemDiscounts || 0) +
          (totals.totalSpecialDiscounts || 0) +
          (totals.totalBillDiscount || 0)
        ).toFixed(2)
      ),
      tax: parseFloat((totals.taxAmount || 0).toFixed(2)),
      shipping: parseFloat(initialShipping || 0),
      total: parseFloat((totals.finalTotal || 0).toFixed(2)),
      payment_type: paymentType,
      cheque_no: paymentType === "cheque" ? chequeNo : null,
      bank_name: paymentType === "cheque" ? bankName : null,
      bank: (paymentType === "online" || paymentType === "card" || paymentType === "cheque") ? selectedBankAccount : null,
      issue_date: paymentType === "cheque" ? issueDate : null,
      received_amount: parseFloat(receivedAmount.toFixed(2)),
      balance_amount: parseFloat(
        (receivedAmount - (totals.finalTotal || 0)).toFixed(2)
      ),
      sale_type: saleType,
      items: products.map((product) => ({
        product_id: product.product_id,
        product_name: product.display_name || product.product_name,
        quantity: parseFloat(product.qty),
        mrp: parseFloat(product.mrp || 0),
        unit_price: parseFloat(product.price || 0),
        discount: parseFloat(product.discount || 0),
        special_discount: parseFloat(product.specialDiscount || 0),
        total: parseFloat(product.total || 0),
        supplier: product.supplier || "N/A",
        category: product.category_name || "N/A",
        store_location: product.store_location || "N/A",
        // Include variant information
        variant_id: product.variant_id || null,
        batch_number: product.batch_number || null,
        expiry_date: product.expiry_date || null,
        free_qty: product.free_qty || 0, // Add the missing free_qty field
      })),
      redeem_amount: redeemAmount,
    };

    try {
      let response;
      if (isUpdateMode && saleId) {
        // Update existing sale
        response = await axios.put(
          `http://127.0.0.1:8000/api/sales/${saleId}`,
          billData,
          getAuthHeaders()
        );
        console.log("Sale updated successfully:", response.data);
      } else {
        // Create new sale
        response = await axios.post(
          "http://127.0.0.1:8000/api/sales",
          billData,
          getAuthHeaders()
        );
        console.log("Bill saved successfully:", response.data);
      }

      setShowSuccessMessage(true);
      setTimeout(() => {
        setShowSuccessMessage(false);
        onClose(true);
        window.location.reload();
      }, 1000);
    } catch (error) {
      console.error(
        isUpdateMode ? "Error updating sale:" : "Error saving bill:",
        error.response?.data || error.message
      );
      alert(
        `Failed to ${isUpdateMode ? "update sale" : "save bill"}: ${error.response?.data?.message || error.message}`
      );
    }
  };

  // Handle sending message
  const handleSendMessage = async () => {
    if (!selectedCustomer?.mobile) {
      alert("Customer mobile number is required to send a message.");
      return;
    }

    try {
      const response = await axios.post(
        "http://127.0.0.1:8000/api/sales-templates/send-message",
        {
          phone: selectedCustomer.mobile,
          message: messageContent,
        },
        getAuthHeaders()
      );
      console.log("Message sent successfully:", response.data);
      setShowMessagePreview(false);
      setShowConfirmation(true);
    } catch (error) {
      console.error(
        "Error sending message:",
        error.response?.data || error.message
      );
      alert(
        `Failed to send message: ${error.response?.data?.message || error.message}`
      );
    }
  };

  // Update renderItemList function with proper alignment
  const renderItemList = (template) => {
    const columns = template.item_list_columns || [
      { id: "no", label: "No", visible: true },
      { id: "name", label: "Name", visible: true },
      { id: "qty", label: "Qty", visible: true },
      { id: "price", label: "Price", visible: true },
      { id: "discount", label: "Discount", visible: true },
      { id: "total", label: "Total", visible: true },
    ];

    const totalSum = products.reduce((sum, product) => {
      const discountPerUnit = (product.discount || 0) / (product.qty || 1);
      const price =
        product.price !== undefined
          ? product.price
          : product.mrp - discountPerUnit;
      const totalDiscount =
        (product.discount || 0) + (product.specialDiscount || 0);
      const total =
        (product.mrp - discountPerUnit) * product.qty -
        (product.specialDiscount || 0);
      return sum + total;
    }, 0);

    return (
      <div
        style={{
          fontFamily: "calibri, sans-serif",
          fontSize: "12px",
          color: "#000000",
        }}
      >
        <div
          style={{ display: "flex", marginBottom: "2px", fontWeight: "bold" }}
        >
          <span style={{ width: "20px", textAlign: "center" }}>No</span>
          <span
            style={{ width: "100px", marginLeft: "5px", textAlign: "left" }}
          >
            Name
          </span>
          <span
            style={{ width: "40px", marginLeft: "5px", textAlign: "right" }}
          >
            Qty
          </span>
          <span
            style={{ width: "50px", marginLeft: "5px", textAlign: "right" }}
          >
            Price
          </span>
          <span
            style={{ width: "50px", marginLeft: "5px", textAlign: "right" }}
          >
            Discount
          </span>
          <span
            style={{ width: "50px", marginLeft: "5px", textAlign: "right" }}
          >
            Total
          </span>
        </div>
        {products.map((product, index) => {
          const discountPerUnit = (product.discount || 0) / (product.qty || 1);
          const price =
            product.price !== undefined
              ? product.price
              : product.mrp - discountPerUnit;
          const totalDiscount =
            (product.discount || 0) + (product.specialDiscount || 0);
          const total =
            (product.mrp - discountPerUnit) * product.qty -
            (product.specialDiscount || 0);
          return (
            <div
              key={`item-${index}`}
              style={{ display: "flex", marginBottom: "1px" }}
            >
              <span style={{ width: "20px", textAlign: "center" }}>
                {index + 1}
              </span>
              <span
                style={{
                  width: "100px",
                  marginLeft: "5px",
                  textAlign: "left",
                  wordWrap: "break-word",
                  overflowWrap: "break-word",
                  lineHeight: "1.2"
                }}
              >
                {product.display_name || product.product_name}
              </span>
              <span
                style={{ width: "40px", marginLeft: "5px", textAlign: "right" }}
              >
                {product.qty} {product.unit_type || ""}
              </span>
              <span
                style={{ width: "50px", marginLeft: "5px", textAlign: "right" }}
              >
                {price.toFixed(2)}
              </span>
              <span
                style={{ width: "50px", marginLeft: "5px", textAlign: "right" }}
              >
                {totalDiscount.toFixed(2)}
              </span>
              <span
                style={{ width: "50px", marginLeft: "5px", textAlign: "right" }}
              >
                {total.toFixed(2)}
              </span>
            </div>
          );
        })}
        <div
          style={{ marginTop: "5px", fontWeight: "bold", textAlign: "right" }}
        >
          Total: {totalSum.toFixed(2)}
        </div>
      </div>
    );
  };

  // const renderDynamicTemplate = (template) => {
  //   const placeholders = {
  //     c1: selectedCustomer?.name || "Walk-in Customer",
  //     t2: new Date().toLocaleDateString(),
  //     t3: billNumber,
  //     t5: paymentType,
  //     t6: companyDetails.company_name,
  //     t7: companyDetails.contact_number,
  //     t8: "{{item_list}}",
  //     t9: totals.subTotalMRP?.toFixed(2) || "0.00",
  //     t11: totals.totalItemDiscounts?.toFixed(2) || "0.00",
  //     t12: totals.finalTotal?.toFixed(2) || "0.00",
  //     t15: receivedAmount.toFixed(2),
  //     t16: balanceAmount.toFixed(2),
  //     t14: "Exchange is allowed within 7 days with original bill.\ntpw;f;fg;gLk; nghUl;fs; xU thuj;jpw;Fs; khj;jpuk; khw;wpf; nfhLf;fg;gLk;",
  //   };

  //   return (
  //     <div
  //       style={{
  //         width: `${template.width}mm`,
  //         height: `${template.height}mm`,
  //         position: "relative",
  //         backgroundColor: "#fff",
  //         padding: "2mm",
  //       }}
  //     >
  //       {template.placeholder_elements.map((key, index) => (
  //         <div
  //           key={`placeholder-${index}`}
  //           style={{
  //             position: "absolute",
  //             left: `${template.placeholder_positions[index].x}px`,
  //             top: `${template.placeholder_positions[index].y}px`,
  //             width: `${template.placeholder_sizes[index].width}px`,
  //             height: `${template.placeholder_sizes[index].height}px`,
  //             fontSize: "12px",
  //             textAlign: "center",
  //             fontFamily: "calibri, sans-serif",
  //             color: "#000000",
  //           }}
  //         >
  //           {key === "t8" ? renderItemList(template) : placeholders[key]}
  //         </div>
  //       ))}
  //       {/* ... rest of the rendering logic */}
  //     </div>
  //   );
  // };

  const renderDynamicTemplate = (template) => {
    const placeholders = {
      c1: selectedCustomer?.name || "Walk-in Customer",
      t2: new Date().toLocaleDateString(),
      t3: billNumber,
      t5: paymentType,
      t6: companyDetails.company_name,
      t7: companyDetails.contact_number,
      t8: "{{item_list}}",
      t9: totals.subTotalMRP?.toFixed(2) || "0.00",
      t11: totals.totalItemDiscounts?.toFixed(2) || "0.00",
      t12: totals.finalTotal?.toFixed(2) || "0.00",
      t15: receivedAmount.toFixed(2),
      t16: balanceAmount.toFixed(2),
      t17: billTime || new Date().toLocaleTimeString(),
      t18: user?.name || user?.username || "Cashier",
      t19: new Date().toLocaleString(),
      t14: "Exchange is allowed within 7 days with original bill.\ntpw;f;fg;gLk; nghUl;fs; xU thuj;jpw;Fs; khj;jpuk; khw;wpf; nfhLf;fg;gLk;",
    };

    return (
      <div
        style={{
          width: `${template.width}mm`,
          height: `${template.height}mm`,
          position: "relative",
          backgroundColor: "#fff",
          padding: "2mm",
        }}
      >
        {/* Render Text Elements */}
        {template.text_elements.map((text, index) => (
          <div
            key={`text-${index}`}
            style={{
              position: "absolute",
              left: `${template.text_positions[index].x}px`,
              top: `${template.text_positions[index].y}px`,
              width: `${template.text_sizes[index].width}px`,
              height: `${template.text_sizes[index].height}px`,
              fontSize: `${template.text_styles[index].fontSize}px`,
              color: template.text_styles[index].color,
              fontWeight: template.text_styles[index].fontWeight,
              textAlign: template.text_styles[index].textAlign,
              fontFamily: template.text_styles[index].fontFamily,
              overflow: "hidden",
            }}
          >
            {text}
          </div>
        ))}

        {/* Render Image Elements */}
        {template.image_elements.map((src, index) => (
          <div
            key={`image-${index}`}
            style={{
              position: "absolute",
              left: `${template.image_positions[index].x}px`,
              top: `${template.image_positions[index].y}px`,
              width: `${template.image_sizes[index].width}px`,
              height: `${template.image_sizes[index].height}px`,
            }}
          >
            <img
              src={src}
              alt={`template-img-${index}`}
              style={{
                width: "100%",
                height: "100%",
                objectFit: "contain",
              }}
            />
          </div>
        ))}

        {/* Render Placeholder Elements */}
        {template.placeholder_elements.map((key, index) => (
          <div
            key={`placeholder-${index}`}
            style={{
              position: "absolute",
              left: `${template.placeholder_positions[index].x}px`,
              top: `${template.placeholder_positions[index].y}px`,
              width: `${template.placeholder_sizes[index].width}px`,
              height: `${template.placeholder_sizes[index].height}px`,
              fontSize: "12px",
              textAlign: "center",
              fontFamily: "calibri, sans-serif",
              color: "#000000",
            }}
          >
            {key === "t8" ? renderItemList(template) : placeholders[key]}
          </div>
        ))}
      </div>
    );
  };
  const handlePrint = () => {
    const template = defaultTemplate || defaultPOSTemplate;
    const printContent = printRef.current;

    // Generate PDF for download
    html2canvas(printContent, { ennale: 2 }).then((canvas) => {
      const imgData = canvas.toDataURL("image/png");
      const pdf = new jsPDF({
        orientation:
          template.width > template.height ? "landscape" : "portrait",
        unit: "mm",
        format: [template.width, template.height],
      });
      pdf.addImage(imgData, "PNG", 0, 0, template.width, template.height);
      pdf.save(`bill-${billNumber}.pdf`);
    });

    // Print via iframe
    const iframe = document.createElement("iframe");
    iframe.style.position = "absolute";
    iframe.style.width = "0px";
    iframe.style.height = "0px";
    iframe.style.border = "none";
    iframe.style.left = "-1000px";
    document.body.appendChild(iframe);

    const iframeDoc = iframe.contentWindow.document;
    iframeDoc.open();
    iframeDoc.write(`
      <html>
        <head>
          <title>Receipt Print</title>
          <style>
            /* Reset and base styles */
            * {
              margin: 0;
              padding: 0;
              box-sizing: border-box;
            }
            body {
              font-family: "calibri", sans-serif;
              font-size: 12px;
              width: 100%;
              margin: 0;
              padding: 2mm;
              color: #000;
              background: white;
            }
            
            /* Header styles */
            .bill-header {
              margin-bottom: 3px;
              text-align: center;
            }
            .bill-header img {
              max-width: 60mm;
              height: auto;
              margin: 0 auto 3px auto;
            }
            .shop-name {
              font-size: 20px;
              font-weight: bold;
              margin-bottom: 2px;
              font-family: "Cressida", Elephant, cursive;
            }
            .shop-name-tamil {
              font-size: 16px;
              font-weight: bold;
              margin-bottom: 2px;
              font-family: "BAMINI-Tamil18", Elephant;
            }
            .shop-address {
              font-size: 13px;
              margin-bottom: 2px;
            }
            .shop-contact {
              font-size: 12px;
              margin-bottom: 3px;
            }
            
            /* Bill info grid */
            .bill-info {
              display: grid;
              grid-template-columns: 1fr 1fr;
              gap: 1px;
              font-size: 12px;
              margin-bottom: 3px;
              padding: 0;
            }
            .bill-info div:nth-child(even) {
              text-align: right;
            }
            
            /* Table styles */
            table {
              width: 100%;
              margin: 5px 0;
              font-size: 12px;
            }
            th, td {
              padding: 1px 0.5px;
            }
            th {
              border-bottom: 1px solid #000;
              border-top: 1px solid #000;
              background-color: #f0f0f0;
              text-align: center;
              font-size: 13px;
            }
            .tr-name td {
              border-bottom: none;
              font-weight: lighter;
              font-size: 14px;
            }
            .tr-details td {
              border-top: none;
              font-size: 13px;
            }
            td:nth-child(1) { width: 8%; text-align: center; }
            td:nth-child(2) { width: 32%; text-align: left; padding-left: 1px; }
            td:nth-child(3) { width: 15%; text-align: right; padding-right: 1px; }
            td:nth-child(4) { width: 15%; text-align: right; padding-right: 1px; }
            td:nth-child(5) { width: 15%; text-align: right; padding-right: 1px; }
            
            /* Summary section */
            .billing-summary {
              margin-top: 5px;
              font-size: 13px;
              padding: 0 1mm;
            }
            .billing-summary h3 {
              font-size: 12px;
              margin-bottom: 2px;
              text-decoration: underline;
            }
            .billing-summary p {
              margin: 1px 0;
            }
            .billing-summary .grand-total {
              font-size: 13px;
              font-weight: bold;
              margin-top: 2px;
            }
            
            /* Footer sections */
            .terms-conditions {
              font-size: 9px;
              margin-top: 5px;
              padding: 2px 1mm 0 1mm;
              border-top: 1px dashed #000;
            }
            .terms-conditions h4 {
              text-align: center;
              margin-bottom: 1px;
              font-size: 10px;
            }
            .terms-tamil {
              font-family: "Adankappidaari", Adankappidaari;
            }
            .thanks {
              font-size: 12px;
              font-weight: bold;
              margin: 3px 0;
              text-align: center;
            }
            .systemby {
              font-size: 8px;
              text-align: center;
              margin-top: 2px;
            }
            .systemby-web {
              font-size: 9px;
              text-align: center;
              font-style: italic;
              margin-bottom: 2px;
            }
            
            /* Utility classes */
            .text-left { text-align: left; }
            .text-center { text-align: center; }
            .text-right { text-align: right; }
            .font-bold { font-weight: bold; }
          </style>
        </head>
        <body>
          ${printContent.innerHTML}
        </body>
      </html>
    `);
    iframeDoc.close();

    iframe.onload = function () {
      setTimeout(() => {
        iframe.contentWindow.focus();
        iframe.contentWindow.print();
        setTimeout(() => {
          document.body.removeChild(iframe);
        }, 500);
      }, 100);
    };
  };

  // Handle confirm actions
  const handleConfirmPrint = () => {
    if (paymentType === "credit") {
      if (!selectedCustomer?.id) {
        alert("Please select a customer for credit sales.");
        return;
      }
    }

    if (paymentType !== "credit" && receivedAmount < totals.finalTotal) {
      alert("Received amount cannot be less than the grand total.");
      return;
    }

    handleSave();
    handlePrint();
  };

  const handleConfirmSave = () => {
    if (paymentType === "credit" || paymentType === "cheque") {
      if (!selectedCustomer?.id) {
        alert("Please select a customer for cheque or credit sales.");
        return;
      }
    }

    setShowConfirmation(false);
    handleSave();
  };

  // Handle new customer form
  const handleNewCustomerChange = (e) => {
    const { name, value, type, checked } = e.target;
    setNewCustomer({
      ...newCustomer,
      [name]: type === "checkbox" ? checked : value,
    });
    setCustomerErrors({ ...customerErrors, [name]: "" });
  };

  const validateNewCustomer = () => {
    const errors = {};
    if (!newCustomer.name.trim()) {
      errors.name = "Customer name is required";
    }
    if (!newCustomer.mobile.trim()) {
      errors.mobile = "Phone number is required";
    }
    if (!newCustomer.nic_number.trim()) {
      errors.nic_number = "NIC number is required";
    }
    return errors;
  };

  const handleAddCustomer = async (e) => {
    e.preventDefault();
    const validationErrors = validateNewCustomer();
    if (Object.keys(validationErrors).length > 0) {
      setCustomerErrors(validationErrors);
      return;
    }

    try {
      const response = await axios.post(
        "http://127.0.0.1:8000/api/customers",
        {
          customer_name: newCustomer.name,
          phone: newCustomer.mobile,
          nic_number: newCustomer.nic_number,
          is_credit_customer: newCustomer.is_credit_customer,
        },
        getAuthHeaders()
      );

      const customersResponse = await axios.get(
        "http://127.0.0.1:8000/api/customers",
        getAuthHeaders()
      );
      setCustomers(customersResponse.data.data);

      setSelectedCustomer({
        id: response.data.id,
        name: response.data.customer_name,
        mobile: response.data.phone,
        is_credit_customer: response.data.is_credit_customer,
      });

      setNewCustomer({
        name: "",
        mobile: "",
        nic_number: "",
        is_credit_customer: false,
      });
      setShowAddCustomerForm(false);
      setCustomerErrors({});
    } catch (error) {
      console.error("Error adding customer:", error);
      alert(
        `Failed to add customer: ${error.response?.data?.message || error.message}`
      );
    }
  };

  // Handle keyboard navigation
  const handleKeyDown = (e) => {
    switch (e.key) {
      case "Enter":
        if (e.target.tagName === "SELECT" && e.target.id === "customerSelect") {
          paymentTypeRef.current?.focus();
        } else if (e.target.id === "receivedAmount") {
          if (!receivedAmount || receivedAmount === 0) {
            setShowMessagePreview(true);
          } else {
            saveButtonRef.current?.focus();
          }
        } else if (e.target.id === "messageContent") {
          return;
        }
        break;
      case "ArrowLeft":
        if (
          showConfirmation &&
          document.activeElement === printButtonRef.current
        ) {
          saveOnlyButtonRef.current?.focus();
        }
        break;
      case "ArrowRight":
        if (
          showConfirmation &&
          document.activeElement === saveOnlyButtonRef.current
        ) {
          printButtonRef.current?.focus();
        }
        break;
      case "Escape":
        if (showConfirmation) {
          setShowConfirmation(false);
        } else if (showMessagePreview) {
          setShowMessagePreview(false);
        } else if (showAddCustomerForm) {
          setShowAddCustomerForm(false);
        }
        break;
      default:
        break;
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm">
      <div
        className="relative bg-white rounded-lg shadow-xl w-full max-w-6xl h-[90vh] overflow-y-auto flex flex-col"
        onKeyDown={handleKeyDown}
      >
        <button
          onClick={() => onClose(false)}
          className="absolute p-2 text-gray-500 rounded-full top-4 right-4 hover:bg-gray-100 hover:text-black"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="w-6 h-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>

        <div className="flex flex-col justify-between gap-6 p-6 md:flex-row">
          {/* Left Column - Customer Info */}
          <div className="w-full space-y-6 md:w-1/2">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <label className="block text-sm font-medium text-gray-700">
                  Select Customer
                </label>
                <button
                  onClick={() => {
                    setShowAddCustomerForm(!showAddCustomerForm);
                    if (!showAddCustomerForm && newCustomerNameRef.current) {
                      setTimeout(() => newCustomerNameRef.current.focus(), 100);
                    }
                  }}
                  className="px-3 py-1 text-xs font-medium text-white bg-indigo-600 rounded-md hover:bg-indigo-700"
                >
                  {showAddCustomerForm ? "Cancel" : "+ Add Customer"}
                </button>
              </div>

              {showAddCustomerForm ? (
                <form
                  onSubmit={handleAddCustomer}
                  className="p-4 space-y-3 rounded-md bg-gray-50"
                >
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Name
                    </label>
                    <input
                      ref={newCustomerNameRef}
                      type="text"
                      name="name"
                      value={newCustomer.name}
                      onChange={handleNewCustomerChange}
                      className="w-full p-2 mt-1 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                      required
                    />
                    {customerErrors.name && (
                      <p className="mt-1 text-sm text-red-600">
                        {customerErrors.name}
                      </p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Mobile
                    </label>
                    <input
                      type="tel"
                      name="mobile"
                      value={newCustomer.mobile}
                      onChange={handleNewCustomerChange}
                      className="w-full p-2 mt-1 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                      required
                    />
                    {customerErrors.mobile && (
                      <p className="mt-1 text-sm text-red-600">
                        {customerErrors.mobile}
                      </p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      NIC Number
                    </label>
                    <input
                      type="text"
                      name="nic_number"
                      value={newCustomer.nic_number}
                      onChange={handleNewCustomerChange}
                      className="w-full p-2 mt-1 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                      required
                    />
                    {customerErrors.nic_number && (
                      <p className="mt-1 text-sm text-red-600">
                        {customerErrors.nic_number}
                      </p>
                    )}
                  </div>
                  <button
                    type="submit"
                    className="w-full px-4 py-2 text-sm font-medium text-white bg-indigo-600 rounded-md hover:bg-indigo-700"
                  >
                    Save Customer
                  </button>
                </form>
              ) : (
                <select
                  ref={customerSelectRef}
                  id="customerSelect"
                  value={selectedCustomer?.id || ""}
                  onChange={handleCustomerChange}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value="">Select a customer</option>
                  {customers.map((customer) => (
                    <option key={customer.id} value={customer.id}>
                      {customer.customer_name} - {customer.phone}
                    </option>
                  ))}
                </select>
              )}
            </div>

            <div className="p-4 space-y-2 rounded-md bg-gray-50">
              <h2 className="text-xl font-bold text-gray-800">
                Customer Information
              </h2>
              <div className="space-y-1">
                <p className="text-sm">
                  <span className="font-medium">Name:</span>{" "}
                  <span className="text-gray-700">
                    {selectedCustomer?.name || "Walk-in Customer"}
                  </span>
                </p>
                <p className="text-sm">
                  <span className="font-medium">Mobile:</span>{" "}
                  <span className="text-gray-700">
                    {selectedCustomer?.mobile || "N/A"}
                  </span>
                </p>
                <p className="text-sm">
                  <span className="font-medium">Loyalty Points:</span>{" "}
                  <span className="text-gray-700">
                    {selectedCustomer?.points_balance != null
                      ? selectedCustomer.points_balance
                      : (selectedCustomer?.points_earned != null && selectedCustomer?.points_redeemed != null)
                        ? (selectedCustomer.points_earned - selectedCustomer.points_redeemed)
                        : "-"}
                  </span>
                </p>
                <p className="text-sm">
                  <span className="font-medium">Date:</span>{" "}
                  <span className="text-gray-700">
                    {new Date().toLocaleDateString()}
                  </span>
                </p>
                <p className="text-sm">
                  <span className="font-medium">Bill No:</span>{" "}
                  <span className="text-gray-700">{billNumber}</span>
                </p>
              </div>
            </div>

            <div className="p-4 space-y-3 rounded-md bg-gray-50">
              <h3 className="text-lg font-semibold text-gray-800">
                Payment Details
              </h3>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Payment Type
                </label>
                <select
                  value={paymentType}
                  ref={paymentTypeRef}
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      e.preventDefault();
                      if (paymentType === "cheque") {
                        chequeNoRef.current?.focus();
                      } else {
                        receivedAmountRef.current?.focus();
                      }
                    }
                  }}
                  onChange={(e) => setPaymentType(e.target.value)}
                  className="w-full p-2 mt-1 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value="cash">Cash</option>
                  <option value="card">Card</option>
                  <option value="online">Online</option>
                  <option value="cheque"
                    ref={chequeRef}
                    onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          e.preventDefault();
                          bankNameRef.current?.focus();
                        }
                      }}
                  >Cheque</option>
                  <option value="credit">Credit</option>
                </select>
              </div>

              {/* Cheque Payment Fields */}
              {paymentType === "cheque" && (
                <div className="p-3 space-y-3 border border-blue-200 rounded-md bg-blue-50">
                  <h4 className="text-sm font-medium text-blue-800">Cheque Details</h4>
                  <div>
                    <label className="block text-xs font-medium text-blue-700">
                      Cheque No
                    </label>
                    <input
                      ref={chequeNoRef}
                      type="text"
                      value={chequeNo}
                      onChange={(e) => setChequeNo(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          e.preventDefault();
                          bankNameRef.current?.focus();
                        }
                      }}
                      className="w-full p-2 mt-1 text-sm border border-blue-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter cheque number"
                    />
                  </div>
                  <div>
                    <label className="block text-xs font-medium text-blue-700">
                      Bank Name
                    </label>
                    <input
                      ref={bankNameRef}
                      type="text"
                      value={bankName}
                      onChange={(e) => setBankName(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          e.preventDefault();
                          issueDateRef.current?.focus();
                        }
                      }}
                      className="w-full p-2 mt-1 text-sm border border-blue-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter bank name"
                    />
                  </div>
                  <div>
                    <label className="block text-xs font-medium text-blue-700">
                      Issue Date
                    </label>
                    <input
                      ref={issueDateRef}
                      type="date"
                      value={issueDate}
                      onChange={(e) => setIssueDate(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          e.preventDefault();
                          receivedAmountRef.current?.focus();
                        }
                      }}
                      className="w-full p-2 mt-1 text-sm border border-blue-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>
              )}

              {/* Bank Account Selection for Online, Card, and Cheque */}
              {(paymentType === "online" || paymentType === "card" || paymentType === "cheque") && (
                <div className="p-3 space-y-3 border border-green-200 rounded-md bg-green-50">
                  <h4 className="text-sm font-medium text-green-800">Bank Account</h4>
                  <div className="relative">
                    <label className="block text-xs font-medium text-green-700">
                      Select Bank Account
                    </label>
                    <input
                      ref={bankAccountRef}
                      type="text"
                      value={bankAccountSearch}
                      onChange={(e) => {
                        setBankAccountSearch(e.target.value);
                        setShowBankAccountDropdown(true);
                      }}
                      onFocus={() => setShowBankAccountDropdown(true)}
                      onBlur={() => setTimeout(() => setShowBankAccountDropdown(false), 200)}
                      className="w-full p-2 mt-1 text-sm border border-green-300 rounded-md focus:ring-green-500 focus:border-green-500"
                      placeholder="Search bank account..."
                    />
                    {showBankAccountDropdown && (
                      <div className="absolute z-50 w-full mt-1 overflow-y-auto bg-white border border-green-300 rounded-md shadow-lg max-h-48">
                        {bankAccounts
                          .filter(account => 
                            account.name.toLowerCase().includes(bankAccountSearch.toLowerCase())
                          )
                          .map((account) => (
                            <div
                              key={`${account.type}-${account.id}`}
                              className="px-3 py-2 text-sm cursor-pointer hover:bg-green-50"
                              onMouseDown={() => {
                                setSelectedBankAccount(`${account.type}-${account.id}`);
                                setBankAccountSearch(account.name);
                                setShowBankAccountDropdown(false);
                              }}
                            >
                              <div className="font-medium">{account.name}</div>
                              <div className="text-xs text-gray-500">{account.account_group}</div>
                            </div>
                          ))}
                        {bankAccounts.filter(account => 
                          account.name.toLowerCase().includes(bankAccountSearch.toLowerCase())
                        ).length === 0 && (
                          <div className="px-3 py-2 text-sm text-gray-500">
                            No bank accounts found
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              )}

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Received Amount
                </label>
                <input
                  id="receivedAmount"
                  ref={receivedAmountRef}
                  type="number"
                  step="0.01"
                  className="w-full p-2 mt-1 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                  value={receivedAmount}
                  onChange={handleReceivedAmountChange}
                />
                {/* Do NOT show receivedAmountError here for redeem error */}
              </div>

              <div >
                <label className="block text-sm font-medium text-gray-700">Redeem</label>
                
                  
                  <input
                    id="redeemAmount"
                    type="number"
                    step="0.01"
                    className="w-full p-2 mt-1 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                    value={redeemAmount}
                    onChange={e => {
                      const val = Number(e.target.value);
                      setRedeemAmount(val);
                      if (val > (selectedCustomer?.points_balance || 0)) {
                        setReceivedAmountError("Redeem amount cannot exceed available loyalty points.");
                      } else {
                        setReceivedAmountError("");
                      }
                    }}
                    placeholder="Enter amount to redeem from loyalty points"
                    min={0}
                    max={selectedCustomer?.points_balance || 0}
                  />
                
              </div>
              {receivedAmountError && (
                <p className="mt-1 text-xs text-red-600">{receivedAmountError}</p>
              )}

              <div className="pt-2 mt-2 border-t border-gray-200">
                <p className="text-sm">
                  <span className="font-medium">Total Paid:</span>{" "}
                  <span className="text-gray-700">
                    {formatCurrency(receivedAmount + redeemAmount)}
                  </span>
                </p>
                <p className="text-sm">
                  <span className="font-medium">Balance:</span>{" "}
                  <span
                    className={
                      balanceAmount >= 0 ? "text-green-600" : "text-red-600"
                    }
                  >
                    {formatCurrency(balanceAmount)}
                  </span>
                </p>
              </div>
            </div>
          </div>

          {/* Right Column - Billing Items */}
          <div className="w-full space-y-6 md:w-1/2">
            <div className="p-4 space-y-3 rounded-md bg-gray-50">
              <h3 className="text-lg font-semibold text-gray-800">
                Billing Items
              </h3>

              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="px-3 py-2 text-xs font-medium tracking-wider text-left text-gray-700 uppercase">
                        No.
                      </th>
                      <th className="px-3 py-2 text-xs font-medium tracking-wider text-center text-gray-700 uppercase">
                        Qty
                      </th>
                      <th className="px-3 py-2 text-xs font-medium tracking-wider text-left text-gray-700 uppercase">
                        Item Name
                      </th>
                      <th className="px-3 py-2 text-xs font-medium tracking-wider text-center text-gray-700 uppercase">
                        MRP
                      </th>
                      <th className="px-3 py-2 text-xs font-medium tracking-wider text-center text-gray-700 uppercase">
                        Discount
                      </th>
                      <th className="px-3 py-2 text-xs font-medium tracking-wider text-center text-gray-700 uppercase">
                        Price
                      </th>
                      <th className="px-3 py-2 text-xs font-medium tracking-wider text-right text-gray-700 uppercase">
                        Total
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {products.map((product, index) => {
                      const unitSpecialDiscount =
                        (product.specialDiscount || 0) / (product.qty || 1);
                      const totalDiscountPerUnit =
                        product.discountPerUnit + unitSpecialDiscount;
                      const price = (product.mrp || 0) - totalDiscountPerUnit;
                      const total = (product.qty || 0) * price;

                      return (
                        <tr key={product.product_id + "-" + index}>
                          <td className="px-3 py-2 text-sm text-gray-700 whitespace-nowrap">
                            {index + 1}
                          </td>
                          <td className="px-3 py-2 text-sm text-center text-gray-700 whitespace-nowrap">
                            {product.qty} {product.unit_type}
                          </td>
                          <td className="px-3 py-2 text-sm text-gray-700 whitespace-nowrap">
                            {product.product_name}
                          </td>
                          <td className="px-3 py-2 text-sm text-center text-gray-700 whitespace-nowrap">
                            {formatCurrency(product.mrp)}
                          </td>
                          <td className="px-3 py-2 text-sm text-center text-gray-700 whitespace-nowrap">
                            {formatCurrency(
                              product.discountPerUnit + unitSpecialDiscount
                            )}
                          </td>
                          <td className="px-3 py-2 text-sm text-center text-gray-700 whitespace-nowrap">
                            {formatCurrency(price)}
                          </td>
                          <td className="px-3 py-2 text-sm font-semibold text-right text-gray-700 whitespace-nowrap">
                            {formatCurrency(total)}
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>

            <div className="p-4 space-y-2 rounded-md bg-gray-50">
              <h3 className="text-lg font-semibold text-gray-800">
                Billing Summary
              </h3>
              <div className="pt-4 mt-4 border-t">
                <p>
                  <strong>Subtotal (MRP):</strong>{" "}
                  {formatCurrency(totals.subTotalMRP)}
                </p>
                <p>
                  <strong>Item Discounts:</strong>{" "}
                  {formatCurrency(totals.totalItemDiscounts)}
                </p>
                <p className="text-lg font-bold">
                  <strong>Grand Total:</strong>{" "}
                  {formatCurrency(totals.finalTotal)}
                </p>
              </div>
              {/* Total Items / Qty line */}
              <div className="flex justify-between">
                <span className="text-sm">Total Items / Qty:</span>
                <span className="text-sm font-medium">
                  {products.length} / {products.reduce((sum, p) => sum + Number(p.qty || 0), 0)}
                </span>
              </div>

              {/* Free Items section directly under Total Items / Qty */}
              {products.some(p => {
                // Find the matching free scheme for display
                const batchInfo = p.batch_number ? ` (Batch: ${p.batch_number})` : "";
                const expiryInfo = p.expiry_date ? ` (Exp: ${p.expiry_date.split("T")[0]})` : "";
                const displayName = `${p.product_name}${batchInfo}${expiryInfo}`.trim().toLowerCase();
                const productName = (p.product_name || "").trim().toLowerCase();
                const categoryName = (p.category_name || "").trim().toLowerCase();
                const freeScheme = activeSchemes.find(
                  (scheme) =>
                    scheme.type === "free" &&
                    scheme.active &&
                    scheme.applies_to === "product" &&
                    (scheme.target?.trim().toLowerCase() === productName ||
                      scheme.target?.trim().toLowerCase() === displayName)
                );
                const freeCatScheme = activeSchemes.find(
                  (scheme) =>
                    scheme.type === "free" &&
                    scheme.active &&
                    scheme.applies_to === "category" &&
                    scheme.target?.trim().toLowerCase() === categoryName
                );
                let appliedScheme = freeScheme || freeCatScheme;
                if (appliedScheme && appliedScheme.buy_quantity && appliedScheme.free_quantity) {
                  const buyQty = Number(appliedScheme.buy_quantity);
                  const freeQty = Number(appliedScheme.free_quantity);
                  const eligibleFree = Math.floor(Number(p.qty) / buyQty) * freeQty;
                  return eligibleFree > 0;
                }
                return false;
              }) && (
                <div className="mt-1 text-sm text-blue-700">
                  <strong>Free Items:</strong>
                  <ul className="ml-4 list-disc">
                    {products.map((p, idx) => {
                      const batchInfo = p.batch_number ? ` (Batch: ${p.batch_number})` : "";
                      const expiryInfo = p.expiry_date ? ` (Exp: ${p.expiry_date.split("T")[0]})` : "";
                      const displayName = `${p.product_name}${batchInfo}${expiryInfo}`.trim().toLowerCase();
                      const productName = (p.product_name || "").trim().toLowerCase();
                      const categoryName = (p.category_name || "").trim().toLowerCase();
                      const freeScheme = activeSchemes.find(
                        (scheme) =>
                          scheme.type === "free" &&
                          scheme.active &&
                          scheme.applies_to === "product" &&
                          (scheme.target?.trim().toLowerCase() === productName ||
                            scheme.target?.trim().toLowerCase() === displayName)
                      );
                      const freeCatScheme = activeSchemes.find(
                        (scheme) =>
                          scheme.type === "free" &&
                          scheme.active &&
                          scheme.applies_to === "category" &&
                          scheme.target?.trim().toLowerCase() === categoryName
                      );
                      let appliedScheme = freeScheme || freeCatScheme;
                      if (appliedScheme && appliedScheme.buy_quantity && appliedScheme.free_quantity) {
                        const buyQty = Number(appliedScheme.buy_quantity);
                        const freeQty = Number(appliedScheme.free_quantity);
                        const eligibleFree = Math.floor(Number(p.qty) / buyQty) * freeQty;
                        if (eligibleFree > 0) {
                          return (
                            <li key={p.product_id + '-' + idx}>
                              {p.display_name || p.product_name}: <strong>{eligibleFree}</strong> (Buy {buyQty} Get {freeQty})
                            </li>
                          );
                        }
                      }
                      return null;
                    })}
                  </ul>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="sticky bottom-0 flex justify-end p-4 bg-white border-t border-gray-200">
          <button
            ref={saveButtonRef}
            onClick={() => {
              if (
                !selectedCustomer?.id ||
                selectedCustomer?.name === "Walk-in Customer"
              ) {
                setShowConfirmation(true);
              } else {
                setShowMessagePreview(true);
              }
            }}
            className="px-6 py-2 text-sm font-medium text-white bg-indigo-600 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            {isUpdateMode ? "Update Sale" : "Save Bill"}
          </button>
        </div>

        {/* Hidden Print Content */}
        <div className="hidden">
          <PrintView
            ref={printRef}
            printType="bill"
            products={initialProducts}
            totals={totals}
            customerInfo={selectedCustomer}
            companyDetails={companyDetails}
            receivedAmount={receivedAmount}
            balanceAmount={balanceAmount}
            paymentType={paymentType}
            billNumber={initialCustomerInfo.bill_number}
            billTime={billTime}
            template={defaultTemplate}
            renderDynamicTemplate={renderDynamicTemplate}
            formatCurrency={formatCurrency}
          />
        </div>

        {/* Message Preview Modal */}
        {showMessagePreview && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
            <div className="w-full max-w-md p-6 bg-white rounded-lg shadow-xl">
              <h3 className="text-lg font-medium leading-6 text-gray-900">
                Message Preview
              </h3>

              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700">
                  Message Content
                </label>
                <textarea
                  id="messageContent"
                  ref={messageContentRef}
                  value={messageContent}
                  onChange={(e) => setMessageContent(e.target.value)}
                  className="w-full h-32 p-2 mt-1 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="Edit your message here..."
                />
                <p className="mt-2 text-sm text-gray-600">
                  This message will be sent to{" "}
                  <span className="font-bold">
                    {selectedCustomer?.mobile || "N/A"}
                  </span>
                </p>
                {messageContent.includes("{{bill_url}}") && (
                  <div className="p-2 mt-2 rounded-md bg-blue-50">
                    <p className="mb-1 text-xs text-blue-600">Bill URL Preview:</p>
                    <a
                      href={`${window.location.origin}/api/bills/${billNumber}/view`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-xs text-blue-500 underline break-all hover:text-blue-700"
                    >
                      {`${window.location.origin}/api/bills/${billNumber}/view`}
                    </a>
                  </div>
                )}
              </div>

              <div className="flex justify-end gap-3 mt-6">
                <button
                  type="button"
                  onClick={() => setShowMessagePreview(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setShowMessagePreview(false);
                    setShowConfirmation(true);
                  }}
                  className="px-4 py-2 text-sm font-medium text-white bg-gray-600 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                >
                  Skip
                </button>
                <button
                  type="button"
                  onClick={handleSendMessage}
                  className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Confirm & Send
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Confirmation Modal */}
        {showConfirmation && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
            <div className="w-full max-w-md p-6 bg-white rounded-lg shadow-xl">
              <h3 className="text-lg font-medium leading-6 text-gray-900">
                Confirm Bill
              </h3>

              <div className="mt-4">
                <p className="text-sm text-gray-600">
                  The bill total is{" "}
                  <span className="font-bold">
                    {formatCurrency(totals.finalTotal)}
                  </span>
                </p>
                <p className="mt-2 text-sm">
                  <span className="font-medium">Balance:</span>{" "}
                  <span
                    className={
                      balanceAmount >= 0
                        ? "text-green-600 font-bold"
                        : "text-red-600 font-bold"
                    }
                  >
                    {formatCurrency(balanceAmount)}
                  </span>
                </p>
                <p className="mt-4 text-sm font-medium text-gray-700">
                  Do you want to print the bill?
                </p>
              </div>

              <div className="flex justify-end gap-3 mt-6">
                <button
                  type="button"
                  onClick={() => setShowConfirmation(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Cancel
                </button>
                <button
                  ref={saveOnlyButtonRef}
                  type="button"
                  onClick={handleConfirmSave}
                  className="px-4 py-2 text-sm font-medium text-white bg-gray-600 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                >
                  No, Just Save
                </button>
                <button
                  ref={printButtonRef}
                  type="button"
                  onClick={handleConfirmPrint}
                  className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Yes, Print
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Success Message */}
        {showSuccessMessage && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
            <div className="p-6 bg-white rounded-lg shadow-xl">
              <div className="flex items-center justify-center w-12 h-12 mx-auto bg-green-100 rounded-full">
                <svg
                  className="w-6 h-6 text-green-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
              <div className="mt-3 text-center">
                <h3 className="text-lg font-medium leading-6 text-gray-900">
                  {isUpdateMode
                    ? "Sale Updated Successfully!"
                    : "Bill Saved Successfully!"}
                </h3>
                <div className="mt-2">
                  <p className="text-sm text-gray-500">
                    {isUpdateMode
                      ? "The sale has been updated in the database."
                      : "The bill has been saved to the database."}
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Error Message */}
        {error && (
          <div className="fixed px-4 py-3 text-red-700 bg-red-100 border border-red-400 rounded bottom-4 right-4">
            <p>{error}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default BillPrintModal;
